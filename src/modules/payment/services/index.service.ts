import {
  BadRequestException,
  HttpException,
  HttpStatus,
  Injectable,
  Logger,
  PreconditionFailedException,
  UnprocessableEntityException,
} from '@nestjs/common';
import { InjectConnection, InjectModel } from '@nestjs/mongoose';
import { Payment, PaymentData, PaymentDocument } from '../payment.schema';
import mongoose, { FilterQuery, Model, UpdateQuery, PaginateModel } from 'mongoose';
import { InitiatePaymentDto, InitiatePublicPaymentDto, InitiateZeepayWalletDebitDto } from '../payment.dto';
import { formatPhoneNumber, formatString, genChars } from '../../../utils';
import {
  MININUM_PAYABLE,
  MOBILE_MONEY_NETWORK,
  PAYMENT_METHODS,
  PAYMENT_PROVIDERS,
  PAYMENT_STATUS,
  PAYMENT_TYPES,
  TRANSFERS_PROVIDERS_PRIORITY,
} from '../../../enums/payment.enum';
import { ConfigService } from '@nestjs/config';
import { PaystackConfig } from '../../../config/types/paystack.config';
import { PaystackRepository } from '../../../repositories/paystack.repository';
import { BrokerTransportService } from '../../../broker/broker-transport.service';
import { Plan } from '../../plan/plan.schema';
import { BROKER_PATTERNS } from '../../../enums/broker.enum';
import { Card, CardDocument } from '../card.schema';
import { PaymentJob, PaymentJobDocument } from '../payment-job.schema';
import { Store } from '../../store/store.schema';
import { Invoice, INVOICE_STATUSES } from '../../invoice/invoice.schema';
import { MonnifyConfig } from '../../../config/types/monnify.config';
import { MonnifyRespository } from '../../../repositories/monnify.repository';
import { ZillaRepository } from '../../../repositories/zilla.repository';
import { ZillaConfig } from '../../../config/types/zilla.config';
import { TRANSACTION_CHANNELS, Wallet } from '../../wallets/wallet.schema';
import { PaymentMethod } from '../../paymentmethod/paymentmethod.schema';
import { UserMessagingRepository } from '../../../repositories/user-messaging.repository';
import {
  applyDiscount,
  blocTransferFeeCalculator,
  flwOneTimaAccountFeeCalculator,
  kpOneTimeAccountFeeCalculator,
  leatherbackFeeCalculator,
  monnifyFeeCalculator,
  monoFeeCalculator,
  payazaOneTimaAccountFeeCalculator,
  paystackFeeCalculator,
  startbuttonFeeCalculator,
  stripeFeeCalculator,
  SUBSCRIPTION_CARD_DISCOUNT,
  SUBSCRIPTION_DD_DISCOUNT,
  thePeerFeeCalculator,
  walletFeeCalculator,
  walletPaymentFeeCalculator,
  zeepayFeeCalculator,
  zillaFeeCalculator,
  sudoFeeCalculator,
} from '../../../utils/fees';
import { MonoRepository } from '../../../repositories/mono.repository';
import { MonoConfig } from '../../../config/types/mono.config';
import { ThepeerConfig } from '../../../config/types/thepeer.config';
import { COUNTRY_CODE, COUNTRY_CURRENCY_MAP, CURRENCIES, CURRENCY_COUNTRY_MAP } from '../../country/country.schema';
import { calculateSubscriptionAmountLeft, cleanString, getDocId, toKobo, toNaira } from '../../../utils/functions';
import { InjectQueue } from '@nestjs/bull';
import { JOBS, QUEUES } from '../../../enums/queues.enum';
import { Queue } from 'bull';
import { PaymentQueueJob } from '../payment.queue';
import { CatlogCredits } from '../../user/credits/credits.schema';
import { Subscription } from '../../subscription/subscription.schema';
import { PaymentSubscriptionService } from './payment.subscription.service';
import { Address, DELIVERY_TYPE, Delivery } from '../../deliveries/deliveries.schema';
import { PaymentDeliveriesSevice } from './payment.deliveries.service';
import { BlocHQRepository } from '../../../repositories/bloc.repository';
import {
  ACCEPTABLE_TOKEN_AMOUNTS,
  AMOUNT_PER_TOKEN,
  TEST_PAYMENT_AMOUNTS,
  TEST_PAYMENT_METHODS,
} from '../../../utils/constants';
import { ADMIN_CONFIG, AdminConfig } from '../../adminconfig/adminconfig.schema';
import { PaymentCouponService } from '../coupons/payment-coupons.service';
import { PaymentCoupon, PaymentCouponDocument } from '../coupons/payment-coupons.schema';
import { computeCouponDiscount } from '../../orders/utils';
import { computePaymentCouponDiscount } from '../coupons/utils';
import { FlutterWaveRepository } from '../../../repositories/flutterwave.repository';
import { PlanOption } from '../../plan/plan-options/plan-options.schema';
import { PaymentTokenService } from './payment.token.service';
import { PLAN_TYPE } from '../../../enums/plan.enum';
import { Order } from '../../orders/order.schema';
import { CreateKpOneTimeAccountResponse, KoraPayRepository } from '../../../repositories/korapay.repository';
import { SlackRepository } from '../../../repositories/slack.respository';
import { ZeepayDebitWalletRequest, ZeepayRepository } from '../../../repositories/zeepay.repository';
import { PayazaRepository } from '../../../repositories/payaza.repository';
import { StartbuttonRepository } from '../../../repositories/startbutton.repository';
import { StartbuttonConfig } from '../../../config/types/startbutton.config';
import { MobileMoneyMethod, MobileMoneyMethodDocument } from '../mobile-money-method.schema';
import { StripeRepository } from '../../../repositories/stripe.repository';
import { LeatherbackConfig } from '../../../config/types/leatherback.config';
import { ResendRepository } from '../../../repositories/resend.repository';
import { DDToken, DDTokenDocument } from '../dd-tokens.schema';
import { CustomerIoRepository } from '../../../repositories/customer-io.repository';
import { PaymentUtilsService } from './payment.utils.service';
import { PaymentDomainPurchaseService } from './payment.domain-purchase.service';
import { SudoRepository } from '../../../repositories/sudo.repository';
import { ZeepayConfig } from '../../../config/types/zeepay.config';

@Injectable()
export class PaymentService {
  public readonly paystackConfig: PaystackConfig;
  public readonly monnifyConfig: MonnifyConfig;
  public readonly zillaConfig: ZillaConfig;
  public readonly monoConfig: MonoConfig;
  public readonly startbuttonConfig: StartbuttonConfig;
  public readonly thepeerConfig: ThepeerConfig;
  public readonly leatherbackConfig: LeatherbackConfig;
  public readonly zeepayConfig: ZeepayConfig;
  public readonly paymentSubscriptionService: PaymentSubscriptionService;
  public readonly paymentDeliveriesService: PaymentDeliveriesSevice;
  public readonly paymentTokenService: PaymentTokenService;
  public readonly paymentDomainPurchaseService: PaymentDomainPurchaseService;
  public readonly paymentUtilsService: PaymentUtilsService;

  constructor(
    @InjectModel(Payment.name)
    public readonly paymentModel: Model<PaymentDocument>,
    @InjectModel(Card.name)
    public readonly cardModel: Model<CardDocument>,
    @InjectModel(DDToken.name)
    public readonly ddTokenModel: Model<DDTokenDocument>,
    @InjectModel(MobileMoneyMethod.name)
    public readonly mobileMoneyMethodModel: Model<MobileMoneyMethodDocument>,
    @InjectConnection()
    public readonly connection: mongoose.Connection,
    configService: ConfigService,
    // paymentCouponService: PaymentCouponService,
    @InjectModel(PaymentCoupon.name)
    public readonly paymentCouponModel: Model<PaymentCouponDocument>,
    @InjectModel(PaymentJob.name)
    protected readonly paymentJobModel: Model<PaymentJobDocument>,
    readonly paystackRepository: PaystackRepository,
    protected readonly monnifyRepository: MonnifyRespository,
    protected readonly zillaRepository: ZillaRepository,
    protected readonly whatsapp: UserMessagingRepository,
    protected readonly mono: MonoRepository,
    readonly startbutton: StartbuttonRepository,
    protected readonly blocRepository: BlocHQRepository,
    protected readonly flwRepository: FlutterWaveRepository,
    protected readonly korayPayRepository: KoraPayRepository,
    protected readonly zeepayRepository: ZeepayRepository,
    protected readonly payazaRepository: PayazaRepository,
    protected readonly stripeRepository: StripeRepository,
    protected readonly sudoRepository: SudoRepository,
    public readonly resend: ResendRepository,
    public readonly brokerTransport: BrokerTransportService,
    public readonly logger: Logger,
    public readonly slack: SlackRepository,
    public readonly customerIo: CustomerIoRepository,
    @InjectQueue(QUEUES.PAYMENT)
    private readonly paymentQueue: Queue<PaymentQueueJob>,
  ) {
    this.logger.setContext('payment.service.ts');
    this.paystackConfig = configService.get<PaystackConfig>('paystackConfiguration');
    this.monnifyConfig = configService.get<MonnifyConfig>('monnifyConfiguration');
    this.zillaConfig = configService.get<ZillaConfig>('zillaConfiguration');
    this.monoConfig = configService.get<MonoConfig>('monoConfiguration');
    this.thepeerConfig = configService.get<ThepeerConfig>('thepeerConfiguration');
    this.startbuttonConfig = configService.get<StartbuttonConfig>('startbuttonConfiguration');
    this.leatherbackConfig = configService.get<LeatherbackConfig>('leatherbackConfiguration');
    this.zeepayConfig = configService.get<ZeepayConfig>('zeepayConfiguration');

    this.paymentSubscriptionService = new PaymentSubscriptionService(
      this,
      paymentModel,
      cardModel,
      mobileMoneyMethodModel,
      brokerTransport,
      logger,
      connection,
      slack,
      resend,
      customerIo,
      // sseService,
    );

    this.paymentDeliveriesService = new PaymentDeliveriesSevice(
      this,
      paymentModel,
      cardModel,
      brokerTransport,
      logger,
      customerIo,
      // sseService,
      connection,
      paymentQueue
    );

    this.paymentTokenService = new PaymentTokenService(
      this,
      paymentModel,
      cardModel,
      brokerTransport,
      logger,
      connection,
      resend,
      customerIo,
      // sseService,
    );

    this.paymentDomainPurchaseService = new PaymentDomainPurchaseService(
      this,
      paymentModel,
      cardModel,
      brokerTransport,
      logger,
      connection,
      resend,
      customerIo,
      // sseService,
    );

    this.paymentUtilsService = new PaymentUtilsService(this);
  }

  /**
   * Refactored version of createPayment that breaks down the logic by payment type
   */
  async createPayment(body: InitiatePaymentDto, storeId: string, handleSuccess: boolean = true) {
    const { type } = body;

    // Get store information
    const store = await this.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, { _id: storeId })
      .toPromise();

    // Validate payment methods
    const availablePaymentMethods = await this.paymentUtilsService.validatePaymentMethods(body, store);

    // Handle coupon if provided
    const coupon = await this.paymentUtilsService.handleCoupon(body, store);

    try {
      let paymentData: PaymentData;

      // Route to the appropriate handler based on payment type
      switch (type) {
        case PAYMENT_TYPES.SUBSCRIPTION:
          paymentData = await this.handleSubscriptionPayment(body, store, coupon, availablePaymentMethods);
          break;
        case PAYMENT_TYPES.DELIVERY:
          paymentData = await this.handleDeliveryPayment(body, store, coupon, availablePaymentMethods);
          break;
        case PAYMENT_TYPES.TOKEN_PURCHASE:
          paymentData = await this.handleTokenPurchasePayment(body, store, coupon, availablePaymentMethods);
          break;
        case PAYMENT_TYPES.DOMAIN_PURCHASE:
          paymentData = await this.handleDomainPurchasePayment(body, store, coupon, availablePaymentMethods);
          break;
        default:
          throw new BadRequestException('Invalid payment type');
      }

      // Handle successful payment if needed
      if (paymentData.status === PAYMENT_STATUS.SUCCESS && handleSuccess) {
        await this.paymentUtilsService.handleSuccessfulPayment(paymentData, type);
      }

      return paymentData;
    } catch (error) {
      if (coupon) {
        await this.rollBackCouponUsage(coupon?.coupon_code, store?.id);
      }
      throw error;
    }
  }

  /**
   * Handles subscription payment processing
   */
  private async handleSubscriptionPayment(
    body: InitiatePaymentDto,
    store: Store,
    coupon: PaymentCouponDocument,
    availablePaymentMethods: PaymentMethod[],
  ): Promise<PaymentData> {
    const { currency, plan: planId, plan_option: planOptionId, payment_methods } = body;

    // Validate required fields
    if (!planId) {
      throw new BadRequestException('A plan id is required for this payment');
    }

    if (!planOptionId) {
      throw new BadRequestException('Please select a plan option');
    }

    if (coupon?.config?.plans?.length > 0 && !coupon?.config?.plans?.includes(planId)) {
      throw new BadRequestException('Coupon is not valid for this subscription plan');
    }

    // Get plan and plan option
    const plan = await this.brokerTransport
      .send<Plan>(BROKER_PATTERNS.PLAN.GET_PLAN, { _id: planId })
      .toPromise();

    if (!plan) {
      throw new BadRequestException('Plan does not exist');
    }

    const planOption = plan.options.find(
      (option: PlanOption & { _id?: string }) => getDocId(option).toString() === planOptionId,
    );

    // Check if the planOptionId is part of the plan options
    if (!planOption) {
      throw new BadRequestException('Invalid plan option selected');
    }

    // Calculate payment amount
    const {
      totalPayable,
      balanceLeft,
      meta,
      narration,
      entityCurrency,
      walletTxChannel,
      walletTxSource,
    } = await this.paymentUtilsService.calculateSubscriptionAmount(
      plan,
      planOption,
      store,
      coupon,
      currency,
      body.upfront,
    );
    // Process payment based on selected payment methods
    return await this.paymentUtilsService.processPaymentMethods(
      payment_methods,
      store,
      totalPayable,
      balanceLeft,
      meta,
      narration,
      entityCurrency,
      walletTxChannel,
      walletTxSource,
      currency,
      PAYMENT_TYPES.SUBSCRIPTION,
    );
  }

  /**
   * Handles delivery payment processing
   */
  private async handleDeliveryPayment(
    body: InitiatePaymentDto,
    store: Store,
    coupon: PaymentCouponDocument,
    availablePaymentMethods: PaymentMethod[],
  ): Promise<PaymentData> {
    const { currency, delivery: deliveryId, payment_methods } = body;

    // Validate required fields
    if (!deliveryId) {
      throw new BadRequestException('A delivery id is required for this payment');
    }

    if (coupon && coupon.payment_type !== PAYMENT_TYPES.DELIVERY) {
      throw new BadRequestException('Coupon is Invalid');
    }

    // Get delivery information
    const delivery = await this.brokerTransport
      .send<Delivery>(BROKER_PATTERNS.DELVERIES.GET_DELIVERY, { _id: deliveryId })
      .toPromise();

    if (!delivery) {
      throw new BadRequestException('Delivery does not exist');
    }

    if (
      (coupon?.config?.provider && coupon?.config?.provider !== delivery.provider) ||
      (coupon?.config?.couriers?.length > 0 && !coupon?.config?.couriers.includes(delivery?.courier?.courier_id))
    ) {
      throw new BadRequestException('Coupon cannot be used for this delivery');
    }

    // Calculate payment amount
    const discountAmount = Boolean(coupon) ? computePaymentCouponDiscount(delivery.delivery_amount, coupon) : 0;
    const amountWithDiscount = delivery.delivery_amount + discountAmount;
    const totalPayable = this.paymentUtilsService.convertToKobo(amountWithDiscount);
    const balanceLeft = totalPayable;

    const meta = {
      delivery: deliveryId,
      store: store?.id,
      coupon: coupon
        ? {
            id: coupon?.id,
            coupon_code: coupon?.coupon_code,
            discount: discountAmount,
            original_amount: this.paymentUtilsService.convertToKobo(delivery.delivery_amount),
          }
        : undefined,
      automatedDelivery: delivery.type === DELIVERY_TYPE.AUTOMATED,
    };

    const narration = `Payment for delivery to ${(delivery.sender_address as Address)?.name}`;
    const entityCurrency = delivery.currency;
    const walletTxChannel = TRANSACTION_CHANNELS.DELIVERY_PAYMENT;
    const walletTxSource = { purpose: 'Delivery payment', name: 'Catlog' };

    // Process payment based on selected payment methods
    return await this.paymentUtilsService.processPaymentMethods(
      payment_methods,
      store,
      totalPayable,
      balanceLeft,
      meta,
      narration,
      entityCurrency,
      walletTxChannel,
      walletTxSource,
      currency,
      PAYMENT_TYPES.DELIVERY,
    );
  }

  /**
   * Handles token purchase payment processing
   */
  private async handleTokenPurchasePayment(
    body: InitiatePaymentDto,
    store: Store,
    coupon: PaymentCouponDocument,
    availablePaymentMethods: PaymentMethod[],
  ): Promise<PaymentData> {
    const { currency, tokens, payment_methods } = body;

    // Validate required fields
    if (!tokens || typeof tokens !== 'number' || !ACCEPTABLE_TOKEN_AMOUNTS.includes(tokens)) {
      throw new BadRequestException('Invalid token amount');
    }

    const storePlan = await this.brokerTransport
      .send<Plan>(BROKER_PATTERNS.PLAN.GET_PLAN, {
        _id: getDocId((store?.subscription as Subscription)?.plan as Plan),
      })
      .toPromise();

    if (storePlan.type !== PLAN_TYPE.KITCHEN || !store?.flags?.uses_chowbot) {
      throw new BadRequestException('Token Purchase is only businesses using chowbot');
    }

    // Calculate payment amount
    const totalPayable = this.paymentUtilsService.convertToKobo(tokens * AMOUNT_PER_TOKEN);
    const balanceLeft = totalPayable;

    const meta = {
      subscription: (store?.subscription as Subscription)?.id,
      store: store?.id,
      token_amount: tokens,
    };

    const narration = `Payment for ${tokens} chowbot tokens`;
    const entityCurrency = currency; // Assuming the currency for token payment is NGN only for now
    const walletTxChannel = TRANSACTION_CHANNELS.TOKEN_PAYMENT;
    const walletTxSource = { purpose: 'Token Purchase', name: 'Catlog' };

    // Process payment based on selected payment methods
    return await this.paymentUtilsService.processPaymentMethods(
      payment_methods,
      store,
      totalPayable,
      balanceLeft,
      meta,
      narration,
      entityCurrency,
      walletTxChannel,
      walletTxSource,
      currency,
      PAYMENT_TYPES.TOKEN_PURCHASE,
    );
  }

  /**
   * Handles domain purchase payment processing
   */
  private async handleDomainPurchasePayment(
    body: InitiatePaymentDto,
    store: Store,
    coupon: PaymentCouponDocument,
    availablePaymentMethods: PaymentMethod[],
  ): Promise<PaymentData> {
    const { currency, domain_purchase: domainPurchaseId, payment_methods } = body;

    // Validate required fields
    if (!domainPurchaseId) {
      throw new BadRequestException('A domain purchase ID is required for this payment');
    }

    // Get domain purchase information
    const domainPurchase = await this.brokerTransport
      .send(BROKER_PATTERNS.DOMAIN.GET_PURCHASE, { purchaseId: domainPurchaseId })
      .toPromise();

    if (!domainPurchase) {
      throw new BadRequestException('Domain purchase does not exist');
    }

    // Calculate payment amount
    const totalPayable = toKobo(domainPurchase.amount);
    const balanceLeft = totalPayable;

    const meta = {
      domain_purchase: domainPurchaseId,
      domain: domainPurchase.domain,
      store: store?.id,
    };

    const narration = `Payment for domain ${domainPurchase.domain}`;
    const entityCurrency = domainPurchase.currency;
    const walletTxChannel = TRANSACTION_CHANNELS.DOMAIN_PURCHASE;
    const walletTxSource = { purpose: 'Domain purchase', name: 'Catlog' };

    // Process payment based on selected payment methods
    return await this.paymentUtilsService.processPaymentMethods(
      payment_methods,
      store,
      totalPayable,
      balanceLeft,
      meta,
      narration,
      entityCurrency,
      walletTxChannel,
      walletTxSource,
      currency,
      PAYMENT_TYPES.DOMAIN_PURCHASE,
    );
  }

  //Payment fee might come from orders (primarily a chowbot modification)
  //If payment_fee exists, then don't calculate a new payment fee
  //payment_fee would have been added to the invoice/order by default
  async createPublicPayment(
    body: InitiatePublicPaymentDto,
    metaData: { [key: string]: any } = {},
    payment_fee?: number,
  ) {
    const { payment_method_type, currency, type, invoice: invoiceId } = body;
    let order: Order;
    let invoice: Invoice;
    let store: Store;

    if (body.type !== 'INVOICE') throw new UnprocessableEntityException('Invalid payment type');

    const availablePaymentMethods = await this.brokerTransport
      .send<PaymentMethod[]>(BROKER_PATTERNS.PAYMENT.GET_ALL_PAYMENT_METHODS, {
        payment_types: { $in: [type] },
        currencies: { $in: currency },
        enabled: true,
      })
      .toPromise();

    const hasInvalidPaymentMethods = !availablePaymentMethods.find((a) => a.type === payment_method_type);

    if (hasInvalidPaymentMethods) {
      throw new BadRequestException('Invalid payment method, please reload page');
    }

    invoice = await this.brokerTransport
      .send<Invoice>(BROKER_PATTERNS.INVOICE.GET_INVOICE, {
        $or: [{ invoice_id: invoiceId }, { invoiceId: invoiceId }],
      })
      .toPromise();

    if (!invoice) {
      throw new BadRequestException('Invoice not found');
    }

    if (invoice.currency !== currency) {
      throw new BadRequestException('Invalid currency, please reload page');
    }

    if (invoice.total_amount < toNaira(MININUM_PAYABLE[currency])) {
      throw new BadRequestException(`Minimum payable amount is ${currency} ${toNaira(MININUM_PAYABLE[currency])} `);
    }

    if (invoice.status === INVOICE_STATUSES.PAID || invoice.status === INVOICE_STATUSES.EXPIRED) {
      throw new BadRequestException('Invoice has been paid or is expired');
    }

    store = await this.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, { _id: invoice.store })
      .toPromise();

    if (!store) {
      throw new BadRequestException('Store not found');
    }

    if (!store.payments_enabled) {
      throw new BadRequestException('This store cannot collect payments at this time');
    }

    if (invoice.order) {
      order = await this.brokerTransport
        .send<Order>(BROKER_PATTERNS.ORDER.GET_ORDER_PROPERTIES, {
          filter: { _id: invoice.order },
          select: 'meta',
        })
        .toPromise();
    }

    let fees = 0;
    let keysObject: any = {};
    let vRef = '';
    let paymentProvider: PAYMENT_PROVIDERS;
    let meta: any = {}; // I think we will more metadata types in the future. Add all here and spread them later
    const ref: string = await this.paymentUtilsService.genRef();
    //CLGPAY-8DIGITS - FIXES

    if (body.payment_method_type === PAYMENT_METHODS.PAYSTACK) {
      fees = payment_fee ? 0 : paystackFeeCalculator(invoice.total_amount, currency, 'external');
      keysObject = {
        publicKey: this.paystackRepository.getPublicKey(currency),
      };
    } else if (body.payment_method_type === PAYMENT_METHODS.THEPEER) {
      fees = payment_fee ? 0 : thePeerFeeCalculator(invoice.total_amount);
      keysObject = {
        publicKey: this.thepeerConfig.publicKey,
      };
    } else if (body.payment_method_type === PAYMENT_METHODS.MOMO) {
      fees = payment_fee ? 0 : zeepayFeeCalculator(invoice.total_amount, this.zeepayConfig.version);
      const allNetworks = Object.values(MOBILE_MONEY_NETWORK);
      keysObject = {
        networks: allNetworks,
      };
      paymentProvider = PAYMENT_PROVIDERS.ZEEPAY;
    } else if (body.payment_method_type === PAYMENT_METHODS.ZILLA) {
      fees = payment_fee ? 0 : zillaFeeCalculator(invoice.total_amount);

      const zillaPayment = await this.zillaRepository.generatePaymentLink({
        amount: Math.ceil(invoice.total_amount + fees),
        clientOrderReference: ref,
        title: invoice.title,
      });

      if (zillaPayment.error)
        throw new HttpException(
          {
            attempt: 'Init Zilla',
            error: zillaPayment.error,
          },
          HttpStatus.INTERNAL_SERVER_ERROR,
        );

      keysObject = {
        publicKey: this.zillaConfig.publicKey,
        zillaOrderId: zillaPayment.data.orderCode,
      };
    } else if (body.payment_method_type === PAYMENT_METHODS.TRANSFER) {
      const config = await this.brokerTransport
        .send<AdminConfig>(BROKER_PATTERNS.CONFIG.GET_CONFIG, ADMIN_CONFIG.TRANSFERS_PROVIDER)
        .toPromise();
      const provider = config?.value ?? PAYMENT_PROVIDERS.MONNIFY;

      const otherProviders = TRANSFERS_PROVIDERS_PRIORITY.filter((p) => p.provider !== provider).sort(
        (a, b) => a.priority - b.priority,
      );
      const allProviders = [provider, ...otherProviders.map((p) => p.provider)];

      for (const method of allProviders) {
        this.logger.log(`ATTEMPTING TO INITIATE TRANSFER WITH ${method}`);
        const trfInititationData = await this.initiatePublicTransferPayment(
          method as PAYMENT_PROVIDERS,
          invoice,
          ref,
          payment_fee,
        );
        if (
          trfInititationData &&
          trfInititationData[0] && //meta
          trfInititationData[1] && //keysObject
          trfInititationData[2] !== null && //fees
          trfInititationData[2] !== undefined //fees
        ) {
          [meta, keysObject, fees, vRef] = trfInititationData;
          paymentProvider = method as PAYMENT_PROVIDERS;
          break;
        }
      }

      if (Object.keys(meta).length < 1 || Object.keys(keysObject).length < 1 || fees === null || fees === undefined) {
        throw new HttpException(
          {
            attempt: 'Init Transfer',
            error: 'Please try another payment method',
          },
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }
    } else if (body.payment_method_type === PAYMENT_METHODS.MONO_DIRECT_PAY) {
      fees = payment_fee ? 0 : monoFeeCalculator(invoice.total_amount);

      const payment = await this.mono.initiatePayment(
        this.paymentUtilsService.convertToKobo(invoice.total_amount + fees),
        ref,
        invoice.title,
      );

      if (payment.error || !payment) {
        throw new HttpException(
          {
            attempt: 'Init Mono',
            error: 'Please try another payment method',
          },
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }

      keysObject = {
        publicKey: this.monoConfig.publicKey,
        monoPaymentId: payment.data.id,
      };
      // paymentProvider = PAYMENT_PROVIDERS.MONO_DIRECT_PAY;
    } else if (body.payment_method_type === PAYMENT_METHODS.STARTBUTTON) {
      fees = payment_fee ? 0 : startbuttonFeeCalculator(invoice.total_amount, currency);

      const payment = await this.startbutton.initiatePayment({
        amount: this.paymentUtilsService.convertToKobo(invoice.total_amount + fees),
        reference: ref,
        currency: currency,
      });

      if (payment.error || !payment || !payment.data || !payment.data.data) {
        throw new HttpException(
          {
            attempt: 'Init Startbutton',
            error: 'Please try another payment method',
          },
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }

      keysObject = {
        publicKey: this.startbuttonConfig.publicKey,
        paymentLink: payment.data.data,
      };
    } else if (body.payment_method_type === PAYMENT_METHODS.STRIPE) {
      fees = payment_fee ? 0 : stripeFeeCalculator(invoice.total_amount);

      const checkoutSession = await this.stripeRepository.createCheckoutSession({
        amount: this.paymentUtilsService.convertToKobo(invoice.total_amount),
        fee: this.paymentUtilsService.convertToKobo(fees),
        reference: ref,
        description: invoice.title,
      });

      if (checkoutSession.error || !checkoutSession || !checkoutSession.data || !checkoutSession.data.url) {
        throw new HttpException(
          {
            attempt: 'Init Stripe Payment',
            error: 'Please try another payment method',
          },
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }

      keysObject = {
        paymentLink: checkoutSession.data.url,
      };
    } else if (body.payment_method_type === PAYMENT_METHODS.LEATHERBACK) {
      fees = payment_fee ? 0 : leatherbackFeeCalculator(invoice.total_amount, currency);
      keysObject = {
        publicKey: this.leatherbackConfig.publicKey,
        customerName: invoice?.receiver?.name,
      };
    }

    //if payment_fee exists it'll have already been added to invoice.total_amount
    const amountWithCharge = this.paymentUtilsService.convertToKobo(invoice.total_amount + (payment_fee ? 0 : fees));

    const payment = await this.paymentModel.create({
      meta: {
        ...meta,
        invoice: body.invoice,
        ...metaData,
      },
      owner: store.owner as any,
      vendorReference: vRef,
      amount_with_charge: amountWithCharge,
      amount: this.paymentUtilsService.convertToKobo(invoice.total_amount - (payment_fee ?? 0)),
      reference: ref,
      status: PAYMENT_STATUS.PENDING,
      payment_method: body.payment_method_type,
      type: PAYMENT_TYPES.INVOICE,
      gateway_charge: 0,
      gateway_amount_settled: 0,
      store: invoice.store,
      currency: currency,
      provider: paymentProvider ?? PAYMENT_PROVIDERS?.SAME_AS_METHOD,
      customer: invoice.receiver,
    });

    if (order?.meta && order?.meta?.chowbot?.db_session_id) {
      await this.brokerTransport
        .send<Order>(BROKER_PATTERNS.WHATSAPP_BOT.UPDATE_PAYMENT_ID, {
          dbSessionId: order?.meta?.chowbot?.db_session_id,
          paymentId: payment?._id,
        })
        .toPromise();
    }

    return {
      currency,
      type: body.payment_method_type,
      total_amount: payment?.payment_method === PAYMENT_METHODS.MOMO ? payment.amount : payment.amount_with_charge,
      fee: this.paymentUtilsService.convertToKobo(fees),
      reference: payment.reference,
      customer: invoice.receiver,
      id: payment._id,
      ...keysObject,
    };
  }

  async updatePayment(filter: FilterQuery<Payment>, update: UpdateQuery<Payment>): Promise<Payment> {
    return this.paymentModel.findOneAndUpdate(filter, update, { new: true }).exec();
  }

  async initiatePublicTransferPayment(
    provider: PAYMENT_PROVIDERS,
    invoice: Invoice,
    ref: string,
    payment_fee?: number,
  ) {
    let data: [any, any, number, string];
    invoice.total_amount = Math.ceil(invoice.total_amount);
    switch (provider) {
      case PAYMENT_PROVIDERS.BLOCHQ:
        data = await this.publicBlocHQPayment(invoice, ref, payment_fee);
        return data;
      case PAYMENT_PROVIDERS.MONNIFY:
        data = await this.publicMonnifyPayment(invoice, ref, payment_fee);
        return data;
      case PAYMENT_PROVIDERS.FLUTTERWAVE:
        data = await this.publicFlwOneTimeAccountPayment(invoice, ref, payment_fee);
        return data;
      case PAYMENT_PROVIDERS.KORAPAY:
        data = await this.publicKoraPayOneTimeAccountPayment(invoice, ref, payment_fee);
        return data;
      case PAYMENT_PROVIDERS.PAYAZA:
        data = await this.publicPayazaOneTimeAccountPayment(invoice, ref, payment_fee);
        return data;
      case PAYMENT_PROVIDERS.SUDO:
        data = await this.publicSudoTransferPayment(invoice, ref, payment_fee);
        return data;
      default:
        return null;
    }
  }

  async createTestPayment(storeId: string, data: { currency: CURRENCIES; payment_method: PAYMENT_METHODS }) {
    const { currency, payment_method } = data;
    const TEST_PAYMENT_AMOUNT = TEST_PAYMENT_AMOUNTS[currency];
    let payment: PaymentDocument;

    const store = await this.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, { _id: storeId })
      .toPromise();

    if (!store) {
      throw new BadRequestException('Store not found');
    }

    const paymentExists = await this.paymentModel.findOne({
      type: PAYMENT_TYPES.TEST,
      store: storeId,
    });

    if (paymentExists && paymentExists.status === PAYMENT_STATUS.SUCCESS) {
      throw new BadRequestException("You've previously made a test payment");
    }

    let paymentMeta = {};

    if (paymentExists && payment_method === PAYMENT_METHODS.DIRECT_TRANSFER) {
      payment = paymentExists;
      payment.reference = await this.paymentUtilsService.genRef(); //regenerate reference
      payment.save();
    } else {
      let fees = 0;
      let amountWithCharge = 0;
      const paymentRef = await this.paymentUtilsService.genRef();

      if (payment_method === PAYMENT_METHODS.PAYSTACK) {
        // fees = paystackFeeCalculator(TEST_PAYMENT_AMOUNT, currency, 'external');
        amountWithCharge = this.paymentUtilsService.convertToKobo(TEST_PAYMENT_AMOUNT + fees);
        paymentMeta = {
          publicKey: this.paystackRepository.getPublicKey(currency),
        };
      }

      if (payment_method === PAYMENT_METHODS.STARTBUTTON) {
        // fees = startbuttonFeeCalculator(TEST_PAYMENT_AMOUNT, currency);
        amountWithCharge = this.paymentUtilsService.convertToKobo(TEST_PAYMENT_AMOUNT + fees);

        const sbPayment = await this.startbutton.initiatePayment({
          amount: amountWithCharge,
          reference: paymentRef,
          currency: currency,
        });

        if (sbPayment?.error || !sbPayment || !sbPayment?.data || !sbPayment?.data?.data) {
          return { error: 'Could not initiate payment, please retry later' };
        } else {
          paymentMeta = {
            publicKey: this.startbuttonConfig.publicKey,
            paymentLink: sbPayment.data.data,
          };
        }
      }

      payment = await this.paymentModel.create({
        meta: {},
        owner: store.owner as any,
        vendorReference: '',
        amount_with_charge: amountWithCharge,
        amount: this.paymentUtilsService.convertToKobo(TEST_PAYMENT_AMOUNT),
        reference: paymentRef,
        status: PAYMENT_STATUS.PENDING,
        provider: PAYMENT_PROVIDERS.SAME_AS_METHOD,
        payment_method: payment_method,
        type: PAYMENT_TYPES.TEST,
        gateway_charge: 0,
        gateway_amount_settled: 0,
        store: storeId,
        currency,
      });

      payment = await payment.save();
    }

    return { ...payment.toJSON(), ...paymentMeta };
  }

  async publicBlocHQPayment(invoice: Invoice, ref: string, payment_fee?: number) {
    let fees = payment_fee ? 0 : blocTransferFeeCalculator(invoice.total_amount);

    const total_amount = Math.ceil(invoice.total_amount + fees);

    const account = await this.blocRepository.createCollectionAccount(total_amount * 100);

    if (account.error || !account || !account.data.account_number) {
      console.log('BLOCHQ ERROR', account.error);
      // throw new HttpException(
      //   {
      //     attempt: 'Init BlocHQ',
      //     error: account.error,
      //   },
      //   HttpStatus.INTERNAL_SERVER_ERROR,
      // );
      return null;
    }

    let vRef = account.data.id as string;

    let keysObject = {
      bank: {
        accountNumber: account.data.account_number,
        accountName: account.data.name,
        bankName: account.data.bank_name,
        amount: total_amount,
      },
    };

    let meta = {
      account: {
        accountNumber: account.data.account_number,
        accountName: account.data.name,
        bankName: account.data.bank_name,
        amount: total_amount,
        bankCode: account.data.bank_code,
      },
    };

    return [meta, keysObject, fees, vRef] as [any, any, number, string];
  }

  async publicFlwOneTimeAccountPayment(
    invoice: Invoice,
    ref: string,
    payment_fee?: number,
  ): Promise<[any, any, number, string]> {
    let fees = payment_fee ? 0 : flwOneTimaAccountFeeCalculator(invoice.total_amount);

    const total_amount = Math.ceil(invoice.total_amount + fees);

    const account = await this.flwRepository.createOneTimeVirtualAccount({
      firstname: cleanString(invoice.receiver?.name.split(' ')[0]),
      lastname: cleanString(invoice.receiver?.name.split(' ')[1]),
      amount: total_amount,
      narration: invoice?.sender?.name,
      tx_ref: ref,
      email: invoice?.receiver?.email ? invoice?.receiver?.email : '<EMAIL>',
      phonenumber: formatPhoneNumber(invoice.receiver?.phone),
    });

    if (account.error || !account || !account?.data?.account_number) {
      console.log('Flutterwave Error ERROR', account?.error);
      // throw new HttpException(
      //   {
      //     attempt: 'Init Flutterwave',
      //     error: account.error,
      //   },
      //   HttpStatus.INTERNAL_SERVER_ERROR,
      // );

      return null;
    }

    let keysObject = {
      bank: {
        accountNumber: account.data.account_number,
        accountName: invoice?.sender?.name,
        bankName: account.data.bank_name,
        amount: total_amount,
      },
    };

    let meta = {
      account: {
        accountNumber: account.data.account_number,
        accountName: invoice?.sender?.name,
        bankName: account.data.bank_name,
        amount: total_amount,
        bankCode: null,
      },
    };

    return [meta, keysObject, fees, ''];
  }

  async publicKoraPayOneTimeAccountPayment(
    invoice: Invoice,
    ref: string,
    payment_fee?: number,
  ): Promise<[any, any, number, string]> {
    let fees = payment_fee ? 0 : kpOneTimeAccountFeeCalculator(invoice.total_amount);

    const total_amount = Math.ceil(invoice.total_amount + fees);

    const account = await this.korayPayRepository.createOneTimeVirtualAccount({
      account_name: cleanString(invoice?.sender?.name),
      amount: total_amount,
      currency: CURRENCIES.NGN,
      reference: ref,
      customer: {
        name: invoice?.receiver?.name,
        email: invoice?.receiver?.email ? invoice?.receiver?.email : '<EMAIL>',
      },
      merchant_bears_cost: true,
    });

    if (account.error || !account || !account?.data?.data?.bank_account?.account_number) {
      this.logger.error('KORAPAY ERROR', account.error);
      return null;
    }

    let keysObject = {
      bank: {
        accountNumber: account.data.data.bank_account.account_number,
        accountName: account.data.data.bank_account.account_name,
        bankName: account.data.data.bank_account.bank_name?.toUpperCase(),
        amount: account.data.data.amount_expected,
      },
    };

    let meta = {
      account: {
        accountNumber: account.data.data.bank_account.account_number,
        accountName: account.data.data.bank_account.account_name,
        bankName: account.data.data.bank_account.bank_name?.toUpperCase(),
        amount: account.data.data.amount_expected,
        bankCode: account.data.data.bank_account.bank_code,
      },
    };

    return [meta, keysObject, fees, ''];
  }

  async publicPayazaOneTimeAccountPayment(
    invoice: Invoice,
    ref: string,
    payment_fee?: number,
  ): Promise<[any, any, number, string]> {
    // Calculate fees based on the Payaza fee structure
    let fees = payment_fee ? 0 : payazaOneTimaAccountFeeCalculator(invoice.total_amount);

    const total_amount = Math.ceil(invoice.total_amount + fees);

    // Prepare the payload for Payaza
    const accountData = await this.payazaRepository.createOneTimeVirtualAccount({
      customer_first_name: cleanString(invoice?.sender?.name?.split(' ')[0]) || '',
      customer_last_name: cleanString(invoice?.sender?.name?.split(' ').slice(1).join(' ')) || 'Store',
      customer_email: '<EMAIL>',
      customer_phone_number: formatPhoneNumber(invoice.receiver?.phone),
      bank_code: '117',
      transaction_amount: total_amount,
      account_reference: ref,
      account_type: 'Dynamic',
      account_name: cleanString(invoice?.sender?.name),
    });

    const account = accountData?.data;

    // Handle error scenarios
    if (accountData.error || !account || !account?.data || !account?.data.account_number) {
      console.log('Payaza Error ERROR', accountData?.error);
      // You may want to throw an exception or handle the error more specifically
      return null;
    }

    // Prepare the meta and key objects similar to the Flutterwave implementation
    let keysObject = {
      bank: {
        accountNumber: account.data.account_number,
        accountName: account.data.account_name,
        bankName: account.data.bank_name,
        amount: total_amount,
      },
    };

    let meta = {
      account: {
        accountNumber: account.data.account_number,
        accountName: account.data.account_name,
        bankName: account.data.bank_name,
        amount: total_amount,
        bankCode: '117',
      },
    };

    return [meta, keysObject, fees, ''];
  }

  async publicMonnifyPayment(invoice: Invoice, ref: string, payment_fee?: number): Promise<[any, any, number, string]> {
    let fees = payment_fee ? 0 : monnifyFeeCalculator(invoice.total_amount);
    const transaction = await this.monnifyRepository.initiateTransaction({
      amount: Math.ceil(invoice.total_amount + fees),
      customerName: invoice.receiver.name,
      customerEmail: invoice.receiver.email || '<EMAIL>',
      paymentDescription: invoice.sender?.name, //sender name because of bank account creation
      paymentReference: ref,
    });

    if (transaction.error || !transaction || !transaction?.data?.transactionReference) {
      this.logger.error('MONNIFY ERROR', transaction.error);
      return null;
    }

    const transfer = await this.monnifyRepository.bankTransfer({
      transactionReference: transaction.data.transactionReference,
      bankCode: '50515',
    });

    if (transfer.error || !transfer || !transfer?.data?.accountNumber) {
      this.logger.error('MONNIFY ERROR', transfer.error);
      return null;
    }

    let meta = {
      monnify_account: {
        accountNumber: transfer.data.accountNumber,
        accountName: transfer.data.accountName,
        bankName: transfer.data.bankName,
        bankCode: transfer.data.bankCode,
        ussdCode: transfer.data.ussdPayment,
        amount: transfer.data.totalPayable,
      },
    };

    let keysObject = {
      publicKey: this.monnifyConfig.apiKey,
      contractCode: this.monnifyConfig.contractCode,
      bank: {
        accountNumber: transfer.data.accountNumber,
        accountName: transfer.data.accountName,
        bankName: transfer.data.bankName,
        bankCode: transfer.data.bankCode,
        ussdCode: transfer.data.ussdPayment,
        amount: transfer.data.totalPayable,
      },
    };

    return [meta, keysObject, fees, ''];
  }

  async ZeepayWalletDebitPayment(data: InitiateZeepayWalletDebitDto) {
    const { payment_reference, network, msisdn } = data;

    const payment = await this.paymentModel.findOne({ reference: payment_reference });
    const store = await this.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, { _id: getDocId(payment?.store ?? payment?.meta?.store) })
      .toPromise();

    // Prepare the payload for Zeepay's debit wallet API
    const debitWalletRequest: ZeepayDebitWalletRequest = {
      customerName: cleanString(store?.name),
      mno: network,
      amount: toNaira(payment.amount_with_charge),
      msisdn: msisdn,
      description: `Payment for ${payment_reference}`,
      reference: payment_reference,
    };

    try {
      // Initiate the wallet debit via Zeepay's API
      const debitResponse = await this.zeepayRepository.debitWallet(debitWalletRequest);

      const responseCode = debitResponse?.data?.code;
      if (debitResponse.error || !debitResponse.data || (this.zeepayConfig.version === 'V1' && responseCode !== 411)) {
        this.logger.error('ZEEPAY WALLET DEBIT ERROR', debitResponse.error);
        throw new BadRequestException('Something went wrong, please try another payment method');
      }

      const responseData =
        this.zeepayConfig.version === 'V1' ? debitResponse.data : (debitResponse as any)?.data?.transaction;

      console.log('<========= ZEEPAY RESPONSE DATA =========>');
      console.log({ responseData, debitResponse });

      payment.meta = {
        ...payment.meta,
        zeepay: {
          msisdn: msisdn,
          mno: network,
          zeepay_id: responseData?.zeepay_id,
        },
      }; //track user payment data
      await payment.save();

      // return debitResponse;
      return { initated: true };
    } catch (error) {
      this.logger.error('ZEEPAY PUBLIC WALLET PAYMENT ERROR', error);
      throw new BadRequestException('Something went wrong, please try another payment method');
    }
  }

  async testPaymentPaid(paymentId: string, gateway_fee: number, gateway_amount_settled: number) {
    const payment = await this.paymentModel.findOne({ _id: paymentId });

    if (!payment) {
      return;
    }

    if (payment.type !== PAYMENT_TYPES.TEST) {
      return;
    }

    payment.status = PAYMENT_STATUS.SUCCESS;
    payment.gateway_charge = gateway_fee;
    payment.gateway_amount_settled = gateway_amount_settled;
    await payment.save();
  }

  async manuallyRecordPayment(data) {
    const {
      reference,
      storeId,
      amount,
      gateway_charge,
      gateway_amount_settled,
      currency,
      payment_method,
      payment_type,
      provider,
      customer,
    } = data;
    const store = await this.brokerTransport
      .send<Store>(BROKER_PATTERNS.STORE.GET_STORE, { _id: storeId })
      .toPromise();

    if (!store) {
      throw new BadRequestException('Store not found');
    }

    let payment = await this.paymentModel.findOne({ reference });

    if (payment) return payment.toJSON();

    payment = await this.paymentModel.create({
      meta: {},
      owner: (store.owner as any)._id,
      vendorReference: reference,
      amount_with_charge: amount,
      amount: amount,
      reference: await this.paymentUtilsService.genRef(),
      status: PAYMENT_STATUS.SUCCESS,
      payment_method: payment_method,
      type: payment_type,
      gateway_charge,
      gateway_amount_settled,
      customer,
      store: storeId,
      currency: currency,
      provider: provider,
    });

    return payment.toJSON();
  }

  async processPublicPayment(
    reference: string,
    settlement: { amount_settled: number; fee: number },
    body: any,
    payment?: PaymentDocument,
    byVendorReference?: boolean,
    meta: any = {},
  ) {
    if (byVendorReference) {
      const ex = new PreconditionFailedException(`Payment with vendor reference ${reference} does not exist`);
      reference = (await this.paymentModel.findOne({ vendorReference: reference }))?.reference;
      if (!reference) throw ex;
    }

    await this.paymentQueue.add(
      QUEUES.PAYMENT,
      {
        type: JOBS.PUBLIC_PAYMENT,
        payload: {
          reference,
          settlement,
          body,
          payment,
          meta,
        },
      },
      {
        delay: 1000 * 2,
      },
    );
  }

  async rollBackPaymentCoupon(p: PaymentDocument) {
    await this.rollBackCouponUsage(p.meta.coupon?.coupon_code, p?.meta?.store);
    // p.meta.coupon = null;
    // p.status = PAYMENT_STATUS.FAILED;
    await this.paymentModel.findOneAndUpdate(
      { reference: p.reference },
      { meta: { ...p.meta, coupon: null }, status: PAYMENT_STATUS.FAILED },
    );
  }

  async rollBackCouponUsage(coupon_code: string, store: string) {
    return await this.paymentCouponModel.updateOne(
      { coupon_code: coupon_code },
      {
        $inc: { quantity: 1 },
        $pull: { used_by: { $in: [store] } },
      },
    );
  }

  async reverseCreditPayment(p: PaymentDocument) {
    await p.updateOne({ status: PAYMENT_STATUS.FAILED });
    await this.brokerTransport
      .send(BROKER_PATTERNS.USER.CREDITS.REVERSE_DEBIT, {
        user_id: p.owner,
        amount: p.amount,
      })
      .toPromise();
  }

  async genRef() {
    let newRef = 'CLGPAY-' + genChars(8, false, true);
    while (await this.paymentModel.exists({ reference: newRef })) {
      newRef = 'CLGPAY-' + genChars(8, false, true);
    }

    return newRef;
  }

  convertToKobo(amount) {
    // return Math.ceil(amount) * 100;
    return Math.ceil(amount * 100);
  }

  async publicSudoTransferPayment(
    invoice: Invoice,
    ref: string,
    payment_fee?: number,
  ): Promise<[any, any, number, string]> {
    // Calculate fees using sudoFeeCalculator
    const fees = payment_fee ?? sudoFeeCalculator(invoice.total_amount);

    const total_amount = Math.ceil(invoice.total_amount + fees);

    // Duration for the virtual account in seconds (e.g., 24 hours)
    const validFor = 24 * 60 * 60;

    // Create the virtual account for payment
    const virtualAccountRequest = {
      validFor,
      settlementAccount: {
        bankCode: process.env.SUDO_SAVE_HAVEN_BANK_CODE || '090286',
        accountNumber: process.env.SUDO_DEBIT_ACCOUNT_NUMBER || '**********',
      },
      callbackUrl: `${process.env.CATLOG_API}/webhooks/sudo`,
      amount: total_amount,
      externalReference: ref,
      customerName: invoice?.receiver?.name || 'Catlog Customer',
      customerEmail: invoice?.receiver?.email || '<EMAIL>',
      customerPhone: invoice?.receiver?.phone || '',
      meta: {
        invoice: invoice.id,
        fees: fees, // Include fees in metadata for reference
      },
    };

    const accountResponse = await this.sudoRepository.createPaymentVirtualAccount(virtualAccountRequest);
    console.log('SUDO ACCOUNT RESPONSE', accountResponse);

    if (accountResponse.error || !accountResponse.data) {
      this.logger.error('SUDO ERROR', accountResponse.error);
      return null;
    }

    const account = accountResponse.data;

    // Save virtual account details in the payment metadata
    let keysObject = {
      bank: {
        accountNumber: account.accountNumber,
        accountName: account.accountName,
        bankName: 'Sudo MFB', // Update with the actual bank name
        amount: total_amount,
      },
    };

    let meta = {
      sudo: {
        virtualAccountId: account._id,
        accountNumber: account.accountNumber,
        accountName: account.accountName,
        bankName: 'Sudo MFB', // Update with the actual bank name
        amount: total_amount,
        validFor: validFor,
        expiryDate: account.expiryDate,
      },
    };

    return [meta, keysObject, fees, ''];
  }

  async getInvoicesFromPaymentReferences(references: string[]) {
    const payments = await this.paymentModel.find({ reference: { $in: references } });
    if (!payments) {
      return [];
    }

    const invoiceIds = payments.map((p) => p.meta.invoice);

    const invoices = await this.brokerTransport
      .send<Invoice[]>(BROKER_PATTERNS.INVOICE.GET_INVOICES, { invoice_id: { $in: invoiceIds } })
      .toPromise();

    return invoices.map((i) => ({
      customer_name: i.receiver.name,
      customer_email: i.receiver?.email,
      customer_phone: i.receiver?.phone?.replace('-', ''),
      amount: i.total_amount,
      order_id: i.order,
      invoice_id: i.id,
      payment_reference: payments.find((p) => p.meta.invoice === i.id)?.reference,
    }));
  }

  async revertInvoicePayments(references: string[]) {
    const paymentIds = [];

    for (const reference of references) {
      const payment = await this.paymentModel.findOneAndUpdate(
        { reference, 'meta.invoice': { $exists: true } },
        { status: PAYMENT_STATUS.FAILED },
        { new: true },
      );

      if (payment) {
        paymentIds.push(getDocId(payment));
        await this.brokerTransport
          .send(BROKER_PATTERNS.WALLET.REVERSE_TRANSACTION, {
            filter: { 'meta.payment_id': getDocId(payment) },
          })
          .toPromise();
      }
    }

    return paymentIds;
  }
}
