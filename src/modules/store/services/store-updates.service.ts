import {
  BadRequestException,
  HttpException,
  HttpStatus,
  Injectable,
  Logger,
  NotFoundException,
  PreconditionFailedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectModel } from '@nestjs/mongoose';
import * as bcrypt from 'bcrypt';
import { isNumber } from 'class-validator';
import { Model } from 'mongoose';
import { BrokerTransportService } from '../../../broker/broker-transport.service';
import { BROKER_PATTERNS } from '../../../enums/broker.enum';
import { PAYMENT_METHODS, PAYMENT_TYPES } from '../../../enums/payment.enum';
import {
  AdditionalStoreDetailsDto,
  AddOrUpdateStoreCategoryStoreDto,
  ToggleLowStockNotificationsDto,
  UpdateChowdeckSettingsDto,
  UpdateCurrenciesDto,
  UpdateDirectCheckoutDto,
  UpdateSecurityPinDto,
  UpdateStoreDto,
  UpdateStoreMaintenanceModeDto,
  UpdateStorePaymentMethodsDto,
} from '../../../models/dtos/StoreDtos';
import { ShipbubbleRepository } from '../../../repositories/shipbubble.repository';
import {
  actionIsAllowed,
  formatPhoneNumber,
  genChars,
  isValidInstagramUsername,
  stripUnderScoreId,
} from '../../../utils';
import { COUNTRY_CODE, COUNTRY_FLAG_EMOJIS, CURRENCIES, Country } from '../../country/country.schema';
import { Address, AddressDocument } from '../../deliveries/deliveries.schema';
import { Item } from '../../item/item.schema';
import { PaymentMethod } from '../../paymentmethod/paymentmethod.schema';
import { DeliveryAreaService } from '../delivery-areas/delivery-areas.service';
import {
  BUSINESS_TYPES,
  CheckoutChannels,
  IStoreCategory,
  Store,
  StoreDocument,
  STOREFRONT_VERSION,
} from '../store.schema';
import { BEST_FIT_CATEGORIES, BUSINESS_CATEGORIES } from '../utils/store-categories';
import BaseStoreService from './base.service';
import { generatePDFFromWebpage } from '../../../utils/generate-pdfs';
import { WebServicesRepository } from '../../../repositories/webservices.repository';
import { S3Repository } from '../../../repositories/s3.repositories';
import { createSubdomainURL, getDocId } from '../../../utils/functions';
import { CreateAddressDto } from '../../../models/dtos/delivery.dto';
import { Subscription } from '../../subscription/subscription.schema';
import { Plan } from '../../plan/plan.schema';
import { SCOPES } from '../../../utils/permissions.util';
import { RpcException } from '@nestjs/microservices';
import { PlanOption } from '../../plan/plan-options/plan-options.schema';
import { SlackRepository } from '../../../repositories/slack.respository';
import { ONBOARDING_STEPS_WITH_REWARDS, User } from '../../user/user.schema';
import { CustomerIoRepository } from '../../../repositories/customer-io.repository';
import { IRequest } from '../../../interfaces/request.interface';
import mongoose from 'mongoose';

@Injectable()
export class StoreUpdatesService extends BaseStoreService {
  constructor(
    @InjectModel(Store.name) protected readonly storeModel: Model<StoreDocument>,
    protected readonly logger: Logger,
    protected readonly brokerTransport: BrokerTransportService,
    protected readonly jwtService: JwtService,
    protected readonly deliveryArea: DeliveryAreaService,
    private readonly shipbubble: ShipbubbleRepository,
    protected readonly webservices: WebServicesRepository,
    protected readonly s3: S3Repository,
    private readonly slack: SlackRepository,
    private readonly customerIo: CustomerIoRepository,
  ) {
    super(storeModel, brokerTransport);
  }

  async update(storeId: string, storeReq: UpdateStoreDto, user?: IRequest['user']) {
    const store = await this.getAndValidateStore(storeId, undefined, true);
    const storePlan = (store?.subscription as Subscription)?.plan as Plan;

    // if (all) {
    //   return this.storeModel
    //     .findByIdAndUpdate(storeId, storeReq, {
    //       new: true,
    //     })
    //     .populate({ path: 'subscription', popuplate: { path: 'plan' } });
    // }

    if (storeReq.phone) {
      for (let index = 0; index < store.checkout_channels.whatsapp.length; index++) {
        if (store.checkout_channels.whatsapp[index].primary) {
          store.checkout_channels.whatsapp[index].phone = storeReq.phone;
          break;
        }
      }
    }

    if (
      storeReq?.configuration?.payment_validates_order &&
      !store?.configuration?.direct_checkout_enabled
      // !(store?.flags?.uses_chowbot && !store?.payments_enabled)
    ) {
      throw new BadRequestException('Please enable direct checkout to ensure users are able to make payments');
    }

    if (!store?.configuration?.require_geolocation && storeReq?.third_party_configs?.chowdeck?.auto_delivery) {
      throw new BadRequestException(
        'Please enable require geolocation to ensure users can provide accurate delivery info',
      );
    }

    if (storeReq?.pickup_address) {
      //Todo: Update this later
      const validatedAddress = await this.shipbubble.validateAddress({
        address: storeReq?.pickup_address,
        email: '<EMAIL>',
        name: `Catlog ${store.name}`,
        phone: store.phone,
      });
      if (validatedAddress.error) throw new BadRequestException(`Invalid pickup address: ${validatedAddress?.error}`);

      if (validatedAddress?.data) {
        const addressDocument = await this.brokerTransport
          .send<AddressDocument>(BROKER_PATTERNS.DELVERIES.SAVE_ADDRESS, {
            store: store.id,
            validatedAddress: validatedAddress?.data,
            phone: store?.phone,
          })
          .toPromise();
        storeReq.pickup_address = addressDocument.id;
      }
    }

    if (
      // store.configuration?.send_menu_on_initiation !== storeReq.configuration?.send_menu_on_initiation && //prevent duplicate updates
      storeReq.configuration?.send_menu_on_initiation === false &&
      store.configuration?.send_menu_on_initiation === true //only trigger if user has previously set send menu on initiation to true
    ) {
      await this.brokerTransport
        .send<Item[]>(BROKER_PATTERNS.ITEM.UPDATE_STORE_ITEMS, {
          store: store.id,
          payload: {
            is_menu_item: storeReq.configuration?.send_menu_on_initiation,
          },
        })
        .toPromise();
    }

    if (
      storeReq.configuration?.facebook_pixel_enabled &&
      !actionIsAllowed({
        plan: storePlan.type,
        planPermission: SCOPES.PLAN_PERMISSIONS.CAN_USE_FACEBOOK_PIXEL,
      })
    ) {
      storeReq.configuration.facebook_pixel_enabled = false;
    }

    // Operation needs refactoring
    const response = await this.storeModel
      .findByIdAndUpdate(
        storeId,
        {
          name: storeReq.name || store.name,
          secondary_phone: storeReq.secondary_phone,
          custom_message: storeReq.custom_message || store.custom_message,
          phone: storeReq.phone || store.phone,
          description: storeReq.description || store.description,
          hero_image: storeReq.hero_image || store.hero_image,
          logo: storeReq.logo || store.logo,
          delivery_locations: storeReq.delivery_locations || store.delivery_locations,
          address: storeReq.address || store.address,
          state: storeReq.state || store.state,
          country: storeReq.country || store.country,
          socials: storeReq.socials ? { ...store.socials, ...storeReq.socials } : store.socials,
          configuration: storeReq.configuration
            ? {
                ...store.configuration,
                ...storeReq.configuration,
                direct_checkout_enabled: store.configuration.direct_checkout_enabled, //to prevent users from updating direct checkout via this endpoint
                color: store?.configuration?.color, //to prevent users from updating colors via this endpoint
                color_cache: store?.configuration?.color_cache,
              }
            : store.configuration,
          payment_validates_order: store?.configuration?.direct_checkout_enabled
            ? storeReq.configuration?.payment_validates_order
            : false, //if direct checkout is disabled, payment validates orders should be disabled
          onboarding_steps: storeReq.onboarding_steps || store.onboarding_steps,
          checkout_channels: store.checkout_channels,
          deliveries_enabled: storeReq.deliveries_enabled || store.deliveries_enabled,
          extra_info: storeReq.extra_info ? { ...store.extra_info, ...storeReq.extra_info } : store.extra_info,
          pickup_address: storeReq?.pickup_address ? storeReq.pickup_address : store.pickup_address,
        },
        { new: true },
      )
      .populate({ path: 'subscription', populate: 'plan' });

    (response as any).customMessage = response.custom_message;

    if (user && user?.primary_store && getDocId(user?.primary_store).toString() === storeId) {
      //extra checks to prevent test from running this
      await this.customerIo.createOrUpdateUser({
        id: getDocId(user),
        email: user.email,
        store_name: store.name,
        store_link: createSubdomainURL(process.env.CATLOG_WWW, store.slug),
        store_country: storeReq.country,
        store_logo: store.logo,
      });
    }

    return await this.populateStoreCountryAnsSubscriptionStatus(response);
  }

  async updateStoreColor(storeId, reqData: { color: string }) {
    const store = await this.storeModel
      .findByIdAndUpdate(storeId, { 'configuration.color': reqData.color })
      .populate({ path: 'subscription', populate: 'plan' });

    return this.populateStoreCountryAnsSubscriptionStatus(store);
  }

  async additionalStoreDetails(user: User, storeId: string, reqData: AdditionalStoreDetailsDto) {
    const updatedSocials = {
      twitter: reqData.social_media_platform === 'twitter' ? reqData.social_media_username : '',
      facebook: reqData.social_media_platform === 'facebook' ? reqData.social_media_username : '',
      instagram: reqData.social_media_platform === 'instagram' ? reqData.social_media_username : '',
      whatsapp: reqData.social_media_platform === 'whatsApp' ? reqData.social_media_username : '',
      snapchat: reqData.social_media_platform === 'snapchat' ? reqData.social_media_username : '',
      tiktok: reqData.social_media_platform === 'tiktok' ? reqData.social_media_username : '',
    };

    const store = await this.storeModel.findByIdAndUpdate(
      storeId,
      {
        business_category: {
          name: reqData.business_category,
          type: reqData.business_type,
          monthly_orders: reqData?.monthly_orders,
          product_types: [], // Assuming this is static or can be updated separately
        },
        socials: updatedSocials,
        logo: reqData.logo,
        description: reqData.description,
      },
      { new: true }, // Return the updated document
    );

    if (!store) {
      throw new Error('Store not found'); // You can use a custom exception
    }

    const country = await this.brokerTransport
      .send<Country>(BROKER_PATTERNS.COUNTRY.GET_COUNTRY, {
        code: store.country,
      })
      .toPromise();

    if (process.env.NODE_ENV === 'production') {
      // Prepare payload for Slack notification
      const slackPayload = {
        name: user.name,
        country: country.name + ' ' + COUNTRY_FLAG_EMOJIS[country.code],
        phone: formatPhoneNumber(user.phone),
        whatsapp: formatPhoneNumber(user.phone).replace('+', ''),
        store_name: store.name,
        store_slug: store.slug,
        business_type: reqData.business_type,
        monthly_orders: reqData.monthly_orders,
        business_category: reqData.business_category,
        social_media_platform: reqData.social_media_platform,
        social_media_username: reqData.social_media_username,
        userId: user.id,
      };

      // Send Slack notification
      try {
        await this.slack.sendSignUpNotification(slackPayload);
      } catch (err) {
        this.logger.error(`Failed to send Slack notification: ${err.message}`);
      }

      const isGoodLead =
        reqData.business_type === BUSINESS_TYPES.PHYSICAL &&
        BEST_FIT_CATEGORIES.includes(reqData.business_category as any);

      if (isGoodLead) {
        await this.brokerTransport
          .send(BROKER_PATTERNS.USER.MARK_USER_AS_QUALIFIED, { userId: getDocId(user) })
          .toPromise();
      }
    }

    await this.customerIo.createOrUpdateUser({
      id: getDocId(user),
      email: user.email,
      business_category: reqData.business_category,
    });

    return store;
  }

  async updateItemsCount(storeId, inc) {
    const oldStoreData = await this.storeModel.findById(storeId);
    const store = await this.storeModel.findByIdAndUpdate(
      storeId,
      [
        { $set: { item_count: { $add: ['$item_count', inc] } } },
        {
          $set: {
            'onboarding_steps.products_added': {
              $cond: { if: { $gt: ['$item_count', 10] }, then: true, else: '$onboarding_steps.products_added' },
            },
          },
        },
      ],
      { new: true },
    );

    if (oldStoreData.item_count < 10 && store.item_count >= 10) {
      await this.brokerTransport
        .send(BROKER_PATTERNS.USER.CREDITS.ADD_CREDITS_FOR_ONBOARDING_STEPS, {
          step: ONBOARDING_STEPS_WITH_REWARDS.UPLOAD_10_PRODUCTS,
          userId: getDocId(store.owner),
        })
        .toPromise();
    }

    return store;
  }

  async updateStoreCategorizations(storeId, reqData: Store['business_category']) {
    const categoryNames = Object.keys(BUSINESS_CATEGORIES).map((c) => c.toLocaleLowerCase());
    const nameIndex = categoryNames.indexOf(reqData.name.toLocaleLowerCase());

    if (nameIndex < 0) {
      throw new BadRequestException('Please provide a valid category name');
    }

    const productTypes = Object.values(BUSINESS_CATEGORIES)[nameIndex].map((type) => type.toLocaleLowerCase());

    if (
      !reqData.product_types ||
      reqData.product_types.length < 1 ||
      reqData.product_types.some((type) => !productTypes.includes(type.toLocaleLowerCase()))
    ) {
      throw new BadRequestException('Please select valid product types');
    }

    const store = await this.storeModel
      .findByIdAndUpdate(storeId, { business_category: reqData })
      .populate({ path: 'subscription', populate: 'plan' });

    return this.populateStoreCountryAnsSubscriptionStatus(store);
  }

  async updateAutoCheckInConfig(storeId, reqData: Store['configuration']['auto_customer_check_in']) {
    const store = await this.storeModel
      .findByIdAndUpdate(storeId, { 'configuration.auto_customer_check_in': reqData })
      .populate({ path: 'subscription', populate: 'plan' });

    return this.populateStoreCountryAnsSubscriptionStatus(store);
  }

  async populateStoreCountryAnsSubscriptionStatus(store: StoreDocument) {
    store = store.toFilteredJSON();
    store.country = await this.brokerTransport
      .send<Country>(BROKER_PATTERNS.COUNTRY.GET_COUNTRY, {
        code: store.country,
      })
      .toPromise();

    return {
      ...store,
      has_paid_subscription: this.getHasPaidSubscription(store),
    };
  }

  async updateSlug(userId: string, id: string, slug: string) {
    slug = String(slug).toLowerCase();
    const slugExists = await this.storeModel.exists({
      $or: [{ slug }, { slugs: slug }, { disabled_slugs: slug }],
    });

    if (slugExists) throw new BadRequestException('This link is currently used by your store or another store');
    let store = await this.storeModel
      .findByIdAndUpdate(id, { slug: slug, disabled_slugs: [], $addToSet: { slugs: slug } }, { new: true })
      .populate({ path: 'subscription', populate: 'plan' });

    return this.populateStoreCountryAnsSubscriptionStatus(store);
  }

  async updateSubscription(
    store_id: string,
    subscription_id: string,
    planOption: PlanOption,
    isPaidSubscription: boolean = false,
    isFreeTrial: boolean = false,
    paidUpfront: boolean = false,
  ) {
    const store = await this.storeModel
      .findOneAndUpdate(
        { _id: store_id },
        {
          subscription: subscription_id,
          has_paid_subscription: isPaidSubscription,
          current_plan: {
            plan_type: planOption.plan_type,
            interval_text: planOption.interval_text,
            interval: planOption.interval,
            on_free_trial: isFreeTrial, //placeholder values
            paid_upfront: paidUpfront,
          },
        },
        { new: true }, // This option makes sure that the updated document is returned
      )
      .populate({
        path: 'subscription',
        populate: 'plan plan_option',
      });

    return this.populateStoreCountryAnsSubscriptionStatus(store);
  }

  /**
   * Adds or updates already saved categories without changing the ids
   * @param storeId
   * @param categoriesReq
   */
  async addOrUpdateCategories(storeId: string, categoriesReq: AddOrUpdateStoreCategoryStoreDto[]) {
    const store = await this.getAndValidateStore(storeId);

    if (store.categories.length === 0) {
      store.categories = categoriesReq;
    } else {
      const cloneStoreCategories = JSON.parse(JSON.stringify(store.categories)); // Clone already saved store categories

      // Loop through freshly updated categories
      categoriesReq.forEach((category) => {
        // Find if a supplied category is already saved before
        const clonedCategory = cloneStoreCategories.find((cloneCategory) => {
          const categoryId = getDocId(category).toString();
          return categoryId && cloneCategory._id.toString() === categoryId;
        });

        if (!clonedCategory) {
          return cloneStoreCategories.push(category);
        }

        clonedCategory.name = category.name;
        clonedCategory.emoji = category.emoji;
        clonedCategory.meta = category?.meta ?? {};
      });

      store.categories = cloneStoreCategories;
    }

    await store.save();

    return await Promise.all(
      stripUnderScoreId<IStoreCategory[]>(store.toJSON().categories).map(async (category) => {
        category.items_count = await this.brokerTransport
          .send<number>(BROKER_PATTERNS.ITEM.GET_TOTAL, {
            tags: { $in: [category.id] },
          })
          .toPromise();
        return category;
      }),
    );
  }

  /**
   * Optimized version of addOrUpdateCategories that reduces database calls
   * @param storeId
   * @param categoriesReq
   */
  async addOrUpdateCategoriesOptimized(storeId: string, categoriesReq: AddOrUpdateStoreCategoryStoreDto[]) {
    const store = await this.getAndValidateStore(storeId);

    // Update categories logic (same as original)
    if (store.categories.length === 0) {
      store.categories = categoriesReq;
    } else {
      // Use Map for O(1) lookups instead of array.find() which is O(n)
      const categoryMap = new Map();
      store.categories.forEach((category) => {
        categoryMap.set(getDocId(category)?.toString(), category);
      });

      // Process updates more efficiently
      categoriesReq.forEach((category) => {
        const categoryId = getDocId(category)?.toString();
        const existingCategory = categoryMap.get(categoryId);
        if (existingCategory) {
          // Update existing category
          existingCategory.name = category.name;
          existingCategory.emoji = category.emoji;
          existingCategory.meta = category?.meta ?? {};
        } else {
          // Add new category
          category._id = new mongoose.Types.ObjectId()?.toHexString();
          category.id = category._id;
          store.categories.push(category);
          categoryMap.set(category._id, category);
        }
      });
    }
    // console.log('<======================', store.categories,'', '=======================>');
    // store.categories = []
    await store.save();

    // Get all category IDs for batch counting
    const categories = stripUnderScoreId<IStoreCategory[]>(store.toJSON().categories);
    const categoryIds = categories.map((category) => category.id);

    if (categoryIds.length === 0) {
      return categories;
    }

    // Single aggregation query to get counts for all categories at once
    const itemCounts = await this.brokerTransport
      .send<{ _id: string; count: number }[]>(BROKER_PATTERNS.ITEM.AGGREGATE_ITEMS_BY_TAGS, {
        storeId,
        categoryIds,
      })
      .toPromise();

    // Create a map for O(1) lookups
    const countMap = new Map(itemCounts.map((item) => [item._id, item.count]));

    // Assign counts to categories
    return categories.map((category) => ({
      ...category,
      items_count: countMap.get(category.id) || 0,
    }));
  }

  async addSingleCategory(storeId: string, category: AddOrUpdateStoreCategoryStoreDto) {
    const store = await this.getAndValidateStore(storeId);

    const cloneStoreCategories = store.categories;
    cloneStoreCategories.push(category);

    store.categories = cloneStoreCategories;
    await store.save();

    return store.categories[store.categories.length - 1];
  }

  async updateStorePaymentMethods(storeId: string, body: UpdateStorePaymentMethodsDto): Promise<Store> {
    const store = await this.storeModel.findById(storeId);

    if (!store) throw new BadRequestException('Store does not exist');
    if (!store.payment_options[body.currency] || store.payment_options[body.currency].length === 0)
      throw new BadRequestException('Currency not supported');

    let hasOneEnabled = false;
    // Only update existing payment methods, don't add new ones from frontend
    store.payment_options[body.currency] = store.payment_options[body.currency].map((existingOption) => {
      // Find if this payment method exists in the request
      const requestOption = body.payment_options.find((m) => m.type === existingOption.type);

      // If found in request, update enabled status, otherwise keep as is
      if (requestOption) {
        if (requestOption.enabled) {
          hasOneEnabled = true;
        }
        return {
          ...existingOption,
          enabled: requestOption.enabled,
        };
      } else {
        // Keep original if not in request
        if (existingOption.enabled) {
          hasOneEnabled = true;
        }
        return existingOption;
      }
    });

    // Mark the field as modified to ensure MongoDB detects the change
    store.markModified('payment_options');

    // Ensure changes are saved to the database
    await store.save();

    return store.toFilteredJSON();
  }

  async setCheckoutChannels(storeId: string, channels: CheckoutChannels) {
    let store = await this.storeModel.findOne({ _id: storeId });

    if (!store) {
      throw new BadRequestException('Store with id does not exist');
    }

    if (channels?.whatsapp?.length == 0) {
      throw new BadRequestException('Stores must have at least one whatsapp checkout channel');
    }

    if (channels?.instagram?.username && !isValidInstagramUsername(channels?.instagram?.username)) {
      throw new BadRequestException('Please provide a valid instagram username');
    } else {
      channels.instagram.id =
        genChars(8) + '-' + genChars(4) + '-' + genChars(4) + '-' + genChars(4) + '-' + genChars(10); // Fake UUIDv4
    }

    for (const index in channels.whatsapp) {
      if (channels.whatsapp[index].primary) store.phone = channels.whatsapp[index].phone;
      channels.whatsapp[index].id =
        genChars(8) + '-' + genChars(4) + '-' + genChars(4) + '-' + genChars(4) + '-' + genChars(10); // Fake UUIDv4
    }

    store.checkout_channels = channels;
    await store.save();

    return store.toFilteredJSON();
  }

  async updateDirectCheckout(storeId: string, data: UpdateDirectCheckoutDto) {
    const store = await this.storeModel.findById(storeId);

    if (!store.configuration.direct_checkout_enabled && data.direct_checkout_enabled) {
      const itemsWithoutQuantities = await this.brokerTransport
        .send<Item[]>(BROKER_PATTERNS.ITEM.CHECK_ITEM_QUANTITIES, { store: storeId })
        .toPromise();

      if (itemsWithoutQuantities.length > 0) {
        throw new HttpException(
          {
            statusCode: '412',
            message: 'Please provide quantities for these items',
            items: stripUnderScoreId(itemsWithoutQuantities),
            error: 'Bad request',
          },
          HttpStatus.PRECONDITION_FAILED,
        );
      }
    }

    if (!data.direct_checkout_enabled) {
      store.configuration.payment_validates_order = false;
    }

    store.configuration.direct_checkout_enabled = data.direct_checkout_enabled;
    store.markModified('configuration'); //my guess is because mongodb cannot detect the change since it's a large object
    await store.save();

    return store.toFilteredJSON();
  }

  async updateChowdeckConfig(storeId: string, data: UpdateChowdeckSettingsDto) {
    const store = await this.storeModel.findById(storeId);

    if (!store) {
      throw new BadRequestException('Store does not exist');
    }

    if (!store?.public_access_tokens?.chowdeck?.reference) {
      throw new BadRequestException('Please contact support to setup chowdeck for your business');
    }

    store.third_party_configs.chowdeck = {
      ...store.third_party_configs.chowdeck,
      ...data,
    };

    if (data?.auto_delivery) {
      store.deliveries_enabled = true;
      store.configuration.require_geolocation = true;
    }

    if (data?.auto_delivery && !store?.pickup_address && !data.pickup_address) {
      throw new BadRequestException('Please provide a pickup address');
    }

    if (data.pickup_address) {
      const address = await this.brokerTransport
        .send<Address>(BROKER_PATTERNS.DELVERIES.CREATE_ADDRESS, {
          storeId: getDocId(store),
          data: {
            address: data.pickup_address,
            phone: store.phone,
            email: '<EMAIL>',
            name: `Catlog ${store.name}`,
          } as CreateAddressDto,
        })
        .toPromise();

      if (!address) {
        throw new BadRequestException("Couldn't validate address, please try again");
      }

      store.pickup_address = getDocId(address);
    }

    store.markModified('configuration');
    store.markModified('third_party_configs'); //my guess is because mongodb cannot detect the change since it's a large object
    await store.save();

    return store.toFilteredJSON();
  }

  async generateMenu(storeId: string) {
    // async generateMenu(storeId: string) {
    console.log('<====', 'GENERATING STORE MENU', '====>');
    const store = await this.storeModel.findById(storeId);

    if (store.flags?.uses_chowbot !== true) throw new BadRequestException('Store is not on kitchen plan');

    try {
      const url = process.env.CATLOG_WWW + `/${store.slug}/menu`;
      const data = await generatePDFFromWebpage(url, 500, 750, {}, 5000, 0.5, this.webservices);

      if (data.error === undefined) {
        const menuS3 = this.s3.withPath('/menus/');
        const upload = await menuS3.uploadBuffer(
          data.pdf,
          'menu-' + store._id + '-' + String(new Date().getTime()) + '.pdf', //AWS isn't overwriting files
          'application/pdf',
        );

        store.store_menu = upload.Location;

        if (!store?.configuration?.send_menu_on_initiation) {
          store.configuration.send_menu_on_initiation = true;
          store.markModified('configuration'); // mongodb cannot detect the change since it's a large object

          await this.brokerTransport
            .send<Item[]>(BROKER_PATTERNS.ITEM.UPDATE_STORE_ITEMS, {
              store: store.id,
              payload: {
                is_menu_item: true,
              },
            })
            .toPromise();
        }
        await store.save();

        return upload.Location;
      }
    } catch (e) {
      console.log(e);
      throw new BadRequestException('Invalid url');
    }

    /* try {
        const url = process.env.CATLOG_WWW + `/${store.slug}/menu`;
        const puppeteer = await import('puppeteer');
        console.log('PUPPETEER PRELAUNCH');
        const browser = await puppeteer.launch({
          executablePath: process.env.CHROME_BIN || null,
          args: ['--no-sandbox', '--media-cache-size=0', '--disk-cache-size=0'],
          defaultViewport: null,
        });
        console.log('PUPPETEER PRE PAGE');
        const page = await browser.newPage();
  
        console.log('New Page');
  
        await page.goto(url, { waitUntil: 'networkidle0' });
        await page.emulateMediaType('screen');
  
        const image = await page.screenshot({
          fullPage: true,
          captureBeyondViewport: true,
        });
  
        await browser.close();
  
        console.log('Browser Close');
        const base64 = (image as Buffer).toString('base64');
        const menuImage = await uploadImage(base64, this.s3, 'RESTAURANT_MENU');
        return menuImage.Location;
      } catch (e) {
        console.log(e);
        throw new BadRequestException('Invalid url');
      } */
  }

  async updateStoreCurrencies(storeId: string, data: UpdateCurrenciesDto) {
    const store = await this.storeModel.findById(storeId);

    const ratesExist = Object.keys(data.rates).length > 0;

    if (ratesExist) {
      Object.values(data.rates).forEach((rate) => {
        if (!isNumber(rate) || rate <= 0)
          throw new PreconditionFailedException('Rates must be a valid number greater than 0');
      });

      Object.keys(data.rates).forEach((curr) => {
        if (!data.storefront.includes(curr as CURRENCIES))
          throw new PreconditionFailedException('Rates can only contain currencies selected for the storefront');
      });
    }

    store.currencies = {
      ...store.currencies,
      products: data.products,
      storefront: data.storefront,
      storefront_default: data.storefront_default,
      rates: ratesExist ? data.rates : null,
    };
    store.markModified('currencies'); //my guess is because mongodb cannot detect the change since it's a large object
    await store.save();

    return store.toFilteredJSON();
  }

  async initPaymentMethods(storeId: string) {
    const store = await this.storeModel.findById(storeId);

    const methods = await this.brokerTransport
      .send<PaymentMethod[]>(BROKER_PATTERNS.PAYMENT.GET_ALL_PAYMENT_METHODS, {})
      .toPromise();

    const paymentOptions = methods
      .filter(
        (m) => m.currencies.includes(store.currencies?.default) && m.payment_types.includes(PAYMENT_TYPES.INVOICE),
      )
      .map((m) => ({
        type: m.type,
        enabled: m.type === PAYMENT_METHODS.ZILLA ? false : true,
      }));

    store.payment_options[store.currencies?.default] = paymentOptions;
    await store.save();

    return store;
  }

  async updateStoreMaintenanceMode(storeId: string, data: UpdateStoreMaintenanceModeDto) {
    const store = await this.storeModel.findById(storeId);
    store.maintenance_mode = data.state;
    await store.save();
    return store.toFilteredJSON();
  }

  async verifySecurityPin(storeId: string, security_pin: string) {
    const store = await this.storeModel.findById(storeId);
    if (!store?.security_pin) {
      throw new RpcException('No security pin set! Please go to payment settings to add a pin');
    }
    return await bcrypt.compare(security_pin, store?.security_pin);
  }

  async updateSecurityPin(userId: string, storeId: string, data: UpdateSecurityPinDto) {
    const correctPassword = await this.brokerTransport
      .send<boolean>(BROKER_PATTERNS.USER.VERIFY_PASSWORD, { id: userId, password: data.password })
      .toPromise();

    if (!correctPassword) {
      throw new PreconditionFailedException('Invalid password provided');
    }

    const store = await this.storeModel.findById(storeId);
    if (!store) {
      throw new BadRequestException('Store does not exist');
    }

    // if (store?.security_pin) {
    //   const correctPin = await bcrypt.compare(data.current_pin, store?.security_pin);
    //   if (!correctPin) throw new PreconditionFailedException('Current pin provided is incorrect');
    // }

    const hashedPin = bcrypt.hashSync(data.new_pin, 10);
    store.security_pin = hashedPin;
    store.onboarding_steps = { ...store.onboarding_steps, security_pin_added: true };
    store.save();
    return await store.toFilteredJSON();
  }

  async updateFirstOrderWithPayment(storeId: string) {
    const store = await this.storeModel.findById(storeId);

    if (store.onboarding_steps?.has_taken_first_order_with_payment) return {};

    const ordersWithPayment = await this.brokerTransport
      .send<number>(BROKER_PATTERNS.ORDER.COUNT_ORDERS, {
        store: storeId,
        is_paid: true,
        $and: [{ payment_id: { $exists: true } }, { payment_id: { $ne: null } }],
      })
      .toPromise();

    if (ordersWithPayment < 1) return {};

    await this.brokerTransport
      .send(BROKER_PATTERNS.USER.CREDITS.ADD_CREDITS_FOR_ONBOARDING_STEPS, {
        step: ONBOARDING_STEPS_WITH_REWARDS.FIRST_ORDER_WITH_PAYMENT,
        userId: getDocId(store.owner),
      })
      .toPromise();

    store.onboarding_steps = { ...store.onboarding_steps, has_taken_first_order_with_payment: true };
    await store.save();

    await this.brokerTransport
      .send(BROKER_PATTERNS.PAYMENT.UPDATE_BIGIN_USER, { subscriptionId: store.subscription })
      .toPromise();

    return store;
  }

  async updateStorefrontVersion(storeId: string, version: STOREFRONT_VERSION) {
    console.log({
      storeId,
      version,
    });
    const store = await this.storeModel.findById(storeId);
    if (!store) {
      throw new BadRequestException('Store does not exist');
    }

    store.flags = {
      ...store.flags,
      storefront_version: version,
    };

    await store.save();
    return this.populateStoreCountryAnsSubscriptionStatus(store);
  }

  async toggleLowStockNotifications(storeId: string, request: ToggleLowStockNotificationsDto) {
    const store = await this.storeModel.findById(storeId);
    if (!store) {
      throw new NotFoundException('Store not found');
    }

    store.configuration.global_stock_threshold = request.threshold || store.configuration.global_stock_threshold;
    store.configuration.low_stock_notifications_enabled = request.enabled;
    await store.save();

    if (request.enabled)
      await this.brokerTransport
        .send(BROKER_PATTERNS.ITEM.ENABLE_LOW_STOCK_TRACKING, {
          storeId,
        })
        .toPromise();

    return this.populateStoreCountryAnsSubscriptionStatus(store);
  }
}
