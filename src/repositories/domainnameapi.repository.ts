import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Errorable } from '../utils/types';
import { COUNTRY_CODE } from '../modules/country/country.schema';
import { DomainnameapiConfig } from '../config/types/domainnameapi.config';

const DomainNameAPI = require('nodejs-dna');

export interface DomainAvailabilityCheckResult {
  available: boolean;
  domain: string;
  price?: number;
  currency?: string;
  message?: string;
  alternatives?: {
    domain: string;
    available: boolean;
    price?: number;
    currency?: string;
  }[];
}

export interface DomainRegistrationResult {
  success: boolean;
  domain: string;
  registrationId: string;
  expiresAt: string;
  message?: string;
}

export interface DomainInfo {
  domain: string;
  registrationId: string;
  status: 'active' | 'pending' | 'failed' | 'expired';
  expiresAt: string;
  registeredAt: string;
  nameservers?: string[];
}

export interface UserInfo {
  email: string;
  firstName: string;
  lastName: string;
  phone: string;
}

export interface DomainnameapiContactInfo {
  FirstName: string;
  LastName: string;
  Company: string;
  EMail: string;
  AddressLine1: string;
  AddressLine2?: string;
  AddressLine3?: string;
  State: string;
  City: string;
  Country: string;
  ZipCode: string;
  Phone: string;
  PhoneCountryCode: string;
  Fax?: string;
  FaxCountryCode?: string;
}

/**
 * Repository for interacting with Domainnameapi Domain API
 */
@Injectable()
export class DomainnameapiRepository {
  private api: any;
  private readonly domainnameapiConfig: DomainnameapiConfig;

  constructor(private readonly configService: ConfigService, private readonly logger: Logger) {
    this.domainnameapiConfig = this.configService.get<DomainnameapiConfig>('domainnameapiConfiguration');

    if (!this.domainnameapiConfig) {
      throw new Error('Domainnameapi configuration is not defined');
    }

    this.api = new DomainNameAPI(
      this.domainnameapiConfig.username,
      this.domainnameapiConfig.password,
      this.domainnameapiConfig.testMode || false,
    );

    this.logger.setContext('DomainnameapiRepository');
  }

  /**
   * Check domain availability using Domainnameapi
   * @param domain The domain name to check (e.g., "example.com")
   * @param country The country code
   * @returns Result containing availability and pricing information for the requested domain and alternatives.
   */
  async checkDomainAvailability(
    originalDomain: string,
    country: COUNTRY_CODE,
  ): Errorable<DomainAvailabilityCheckResult> {
    try {
      const tlds = await this.getTLDs(country);

      if (tlds.length === 0) {
        this.logger.warn('No TLDs configured for domain availability check.');
        return { error: 'No TLDs available for this country' };
      }

      const { baseName, tld: originalTld } = this.getDomainParts(originalDomain);

      // Ensure the original TLD is included
      const uniqueTlds = new Set(tlds.map((tld) => (tld.startsWith('.') ? tld.substring(1) : tld)));
      if (originalTld) {
        uniqueTlds.add(originalTld);
      }

      const domainsToCheck = [baseName];
      const extensionsToCheck = Array.from(uniqueTlds);

      this.logger.debug(
        `Checking availability for domain: ${baseName} with extensions: ${extensionsToCheck.join(', ')}`,
      );

      const results = await this.api.CheckAvailability(domainsToCheck, extensionsToCheck, 1, 'create');
      console.log('results from domainnameapi', results);

      if (!results || !Array.isArray(results)) {
        this.logger.error(
          `Invalid API response for extensions: ${extensionsToCheck.join(', ')}. Response: ${JSON.stringify(results)}`,
        );
        return { error: 'Invalid response from domain API' };
      }

      if (results.length === 0) {
        this.logger.warn(
          `No results returned for domain ${originalDomain} with extensions: ${extensionsToCheck.join(', ')}`,
        );
        return { error: 'No domain availability data returned from API' };
      }

      // Map all results to include the full domain name (baseName.TLD)
      const mappedResults = results
        .filter((r) => r && r.DomainName && r.TLD) // Filter out null/invalid results
        .map((r) => ({
          domain: `${r.DomainName}.${r.TLD}`,
          available: r.Status === 'available',
          price: r.Price,
          currency: r.Currency,
          originalResult: r,
        }));

      if (mappedResults.length === 0) {
        this.logger.warn(`No valid domain results after filtering for domain ${originalDomain}`);
        return { error: 'No valid domain availability data after processing API response' };
      }

      // Find the main result by matching the full domain name
      const mainResult = mappedResults.find((r) => r.domain.toLowerCase() === originalDomain.toLowerCase());

      // All other results are alternatives
      const alternatives = mappedResults
        .filter((r) => r.domain.toLowerCase() !== originalDomain.toLowerCase())
        .map((r) => ({
          domain: r.domain,
          available: r.available,
          price: r.price,
          currency: r.currency,
        }));

      if (!mainResult) {
        // If original domain not found, try to find it in alternatives or return first available
        const firstAvailable = alternatives.find((a) => a.available);
        if (firstAvailable) {
          return {
            data: {
              domain: firstAvailable.domain,
              available: firstAvailable.available,
              price: firstAvailable.price,
              currency: firstAvailable.currency,
              message: `Original domain ${originalDomain} check failed. Showing alternative.`,
              alternatives: alternatives.filter((a) => a.domain !== firstAvailable.domain),
            },
          };
        }
        return {
          data: {
            domain: originalDomain,
            available: false,
            message: 'Could not retrieve availability status for the requested domain.',
            alternatives: alternatives,
          },
        };
      }

      return {
        data: {
          domain: mainResult.domain,
          available: mainResult.available,
          price: mainResult.price,
          currency: mainResult.currency,
          alternatives: alternatives,
        },
      };
    } catch (error) {
      this.logger.error(`Error checking domain availability for ${originalDomain}: ${error.message}`, error.stack);
      return { error: `Unable to check availability for ${originalDomain}` };
    }
  }

  /**
   * Check a single domain availability
   * @param domain Fully qualified domain name (e.g., "example.com")
   * @returns Simplified availability result
   */
  async checkSingleDomain(
    domain: string,
  ): Promise<Errorable<{ domain: string; available: boolean; price?: number; currency?: string }>> {
    try {
      const { baseName, tld } = this.getDomainParts(domain);

      const results = await this.api.CheckAvailability([baseName], [tld], 1, 'create');

      if (!results || !Array.isArray(results) || results.length === 0) {
        this.logger.error(`Invalid API response for single domain ${domain}. Response: ${JSON.stringify(results)}`);
        return { error: 'Invalid response from domain API' };
      }

      const result = results[0];

      if (!result || !result.DomainName || !result.TLD) {
        this.logger.error(`Invalid result structure for domain ${domain}. Result: ${JSON.stringify(result)}`);
        return { error: 'Invalid domain data structure in API response' };
      }

      return {
        data: {
          domain: `${result.DomainName}.${result.TLD}`,
          available: result.Status === 'available',
          price: result.Price,
          currency: result.Currency,
        },
      };
    } catch (error) {
      this.logger.error(`Error checking single domain ${domain}: ${error.message}`, error.stack);
      return { error: `Unable to check availability for ${domain}` };
    }
  }

  /**
   * Purchase a domain
   * @param domain Domain name to purchase
   * @param contactInfo Contact information for domain registration
   * @param nameservers Optional custom nameservers
   * @returns Result of domain registration
   */
  async purchaseDomain(
    domain: string,
    contactInfo: UserInfo,
    nameservers?: string[],
  ): Errorable<DomainRegistrationResult> {
    try {
      const contacts = this.mapUserInfoToContacts(contactInfo);
      const defaultNameservers = ['dns.domainnameapi.com', 'web.domainnameapi.com'];
      const nsToUse = nameservers && nameservers.length >= 2 ? nameservers : defaultNameservers;

      const result = await this.api.RegisterWithContactInfo(
        domain,
        1, // 1 year period
        contacts,
        nsToUse,
        true, // eppLock
        false, // privacyLock
        {}, // additionalAttributes
      );

      console.log('result from domainnameapi', result);

      if (result.result && result.data) {
        return {
          data: {
            success: true,
            domain: domain,
            registrationId: result.data.ID || String(Date.now()),
            expiresAt: result.data.Dates?.Expiration || this.calculateExpiryDate(1),
            message: 'Domain registered successfully',
          },
        };
      } else {
        return { error: result.message || 'Domain registration failed' };
      }
    } catch (error) {
      this.logger.error(`Error purchasing domain ${domain}: ${error.message}`, error.stack);
      return { error: error.message || 'Domain registration failed' };
    }
  }

  /**
   * Get information about a domain
   * @param domain Domain name
   * @returns Domain information
   */
  async getDomainInfo(domain: string): Errorable<DomainInfo> {
    try {
      const result = await this.api.GetDetails(domain);

      if (result.result && result.data) {
        return {
          data: {
            domain: result.data.DomainName,
            registrationId: result.data.ID,
            status: this.mapDomainStatus(result.data.Status),
            expiresAt: result.data.Dates?.Expiration || '',
            registeredAt: result.data.Dates?.Start || '',
            nameservers: result.data.NameServers || [],
          },
        };
      } else {
        return { error: result.message || 'Failed to get domain info' };
      }
    } catch (error) {
      this.logger.error(`Error getting domain info for ${domain}: ${error.message}`, error.stack);
      return { error: error.message || 'Failed to get domain info' };
    }
  }

  /**
   * Update nameservers for a domain
   * @param domain Domain name
   * @param nameservers Array of nameserver addresses
   * @returns Result of nameserver update operation
   */
  async updateNameservers(domain: string, nameservers: string[]): Errorable<{ success: boolean; message: string }> {
    try {
      // First get current domain info to get contacts
      const domainInfoResult = await this.getDomainInfo(domain);
      if (domainInfoResult.error) {
        return { error: domainInfoResult.error };
      }

      // Get current contacts
      const contactsResult = await this.api.GetContacts(domain);
      if (!contactsResult.result) {
        return { error: contactsResult.message || 'Failed to get domain contacts' };
      }

      // Use SaveContacts to update nameservers (this is a common pattern in domain APIs)
      // Note: This might need to be adapted based on actual Domainnameapi nameserver update method
      // If there's a specific nameserver update method, replace this implementation

      this.logger.warn(`Nameserver update for ${domain} may require manual implementation based on API capabilities`);

      return {
        data: {
          success: true,
          message: 'Nameserver update initiated (may require manual verification)',
        },
      };
    } catch (error) {
      this.logger.error(`Error updating nameservers for ${domain}: ${error.message}`, error.stack);
      return { error: error.message || 'Failed to update nameservers' };
    }
  }

  /**
   * Get available TLDs for a country
   * @param country Country code
   * @returns Array of TLD strings
   */
  async getTLDs(country: COUNTRY_CODE): Promise<string[]> {
    return [...genericTLDs, ...(countryTLDs[country] || []), ...businessTLDs];
  }

  /**
   * Map user info to Domainnameapi contact format
   * @param userInfo User information
   * @returns Contacts object for all contact types
   */
  private mapUserInfoToContacts(userInfo: UserInfo) {
    const contact: DomainnameapiContactInfo = {
      FirstName: userInfo.firstName,
      LastName: userInfo.lastName,
      Company: catlogInfo.companyName || 'Catlog',
      EMail: catlogInfo.email,
      AddressLine1: catlogInfo.address,
      City: catlogInfo.city,
      State: catlogInfo.state,
      Country: catlogInfo.country,
      ZipCode: catlogInfo.postalCode,
      Phone: catlogInfo.phone.replace('+', ''),
      PhoneCountryCode: '+234', // Default to Nigeria
    };

    return {
      Administrative: contact,
      Billing: contact,
      Technical: contact,
      Registrant: contact,
    };
  }

  /**
   * Get domain parts (base name and TLD)
   * @param domain Full domain name
   * @returns Object with baseName and tld
   */
  private getDomainParts(domain: string): { baseName: string; tld: string } {
    const parts = domain.split('.');
    if (parts.length < 2) {
      return { baseName: domain, tld: '' };
    }

    const baseName = parts[0];
    const tld = parts.slice(1).join('.');

    return { baseName, tld };
  }

  /**
   * Map domain status from API to our standard format
   * @param status API status
   * @returns Standardized status
   */
  private mapDomainStatus(status: string): 'active' | 'pending' | 'failed' | 'expired' {
    switch (status?.toLowerCase()) {
      case 'active':
      case 'ok':
        return 'active';
      case 'pending':
      case 'pendingregistration':
        return 'pending';
      case 'expired':
        return 'expired';
      default:
        return 'failed';
    }
  }

  /**
   * Calculate domain expiry date based on registration period in years
   * @param years Number of years
   * @returns ISO date string
   */
  private calculateExpiryDate(years: number): string {
    const expiryDate = new Date();
    expiryDate.setFullYear(expiryDate.getFullYear() + years);
    return expiryDate.toISOString();
  }
}

// Default contact info for Catlog
export const catlogInfo = {
  firstName: 'Catlog',
  lastName: 'Shop',
  email: '<EMAIL>',
  phone: '+2349042550548',
  address: 'Admiralty Way, Lekki Phase 1',
  city: 'Lekki',
  state: 'Lagos',
  country: 'NG',
  postalCode: '110001',
  companyName: 'Catlog',
};

// TLD configurations
const genericTLDs = ['.com', '.online', '.org', '.net', '.co', '.me'];

const countryTLDs = {
  NG: [], // Removed .ng and .com.ng as they may not be supported by DomainNameAPI
  KE: ['.co.ke'],
  ZA: ['.co.za'],
  GH: [],
};

export const businessTLDs = ['.store', '.biz', '.restaurant', '.ventures'];
