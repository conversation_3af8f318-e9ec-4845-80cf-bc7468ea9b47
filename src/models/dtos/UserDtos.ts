import { ApiProperty, PickType } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsString,
  IsOptional,
  IsBoolean,
  IsDateString,
  IsObject,
  IsEnum,
  IsEmail,
  IsNumber,
  IsArray,
} from 'class-validator';
import { COUNTRY_CODE } from '../../modules/country/country.schema';
import { STORE_TYPES } from '../../modules/store/store.schema';
import { INTERNAL_ROLES } from '../../utils/permissions.util';
import { NOTIFICATION_TYPE } from '../../modules/user/notifications/notification.schema';

export class CreateUserDto {
  @ApiProperty({ example: 'Tonia' })
  @IsString()
  @IsNotEmpty()
  public readonly name: string;

  @ApiProperty({ example: '<EMAIL>' })
  @IsString()
  @IsNotEmpty()
  public readonly email: string;

  @ApiProperty({ example: '#Example901' })
  @IsString()
  @IsNotEmpty()
  public readonly password: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  public readonly recaptcha_token?: string;

  @ApiProperty({ example: '08022233322' })
  @IsString()
  @IsNotEmpty()
  public readonly phone: string;

  @ApiProperty({ example: 'my-store-x549' })
  @IsString()
  @IsOptional()
  public readonly referral_code?: string;

  @ApiProperty({
    type: 'object',
    description:
      'Dynamic object for tracking ad source data. Can include any properties like utm_source, utm_medium, utm_campaign, gclid, fbp, fbc, ip_address, user_agent, ttp, ttclid, etc.',
    additionalProperties: true,
    properties: {
      utm_source: {
        type: 'string',
        description: 'UTM source parameter',
      },
      utm_medium: {
        type: 'string',
        description: 'UTM medium parameter',
      },
      utm_campaign: {
        type: 'string',
        description: 'UTM campaign parameter',
      },
      gclid: {
        type: 'string',
        description: 'Google Click ID',
      },
      fbp: {
        type: 'string',
        description: 'Facebook pixel ID',
      },
      fbc: {
        type: 'string',
        description: 'Facebook click ID',
      },
      ip_address: {
        type: 'string',
        description: 'IP address of the user',
      },
      user_agent: {
        type: 'string',
        description: 'User agent string',
      },
      ttp: {
        type: 'string',
        description: 'TikTok pixel ID',
      },
      ttclid: {
        type: 'string',
        description: 'TikTok click ID',
      },
    },
  })
  @IsOptional()
  @IsObject()
  public readonly source_ad?: Record<string, any>;

  @ApiProperty()
  @IsString()
  @IsOptional()
  country?: COUNTRY_CODE;
}

export class CreateUserDtoV2 extends CreateUserDto {
  @ApiProperty({ example: 'Tola Hairs' })
  @IsString()
  @IsNotEmpty()
  public readonly business_name: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @IsEnum(COUNTRY_CODE)
  country: COUNTRY_CODE;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @IsEnum(STORE_TYPES)
  store_type: STORE_TYPES;
}

// export class CreateThirdPartyUserDto extends CreateUserDto {

// }

export class LoginUserDto {
  @ApiProperty()
  @IsString()
  public readonly email: string;

  @ApiProperty()
  @IsString()
  public readonly password: string;

  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  public readonly forced: boolean;
}

export class OrderLoginUserDto extends LoginUserDto {
  @ApiProperty()
  @IsString()
  public readonly order_id: string;
}

export class InternalLoginUserDto {
  @ApiProperty()
  @IsString()
  public readonly username: string;

  @ApiProperty()
  @IsString()
  public readonly password: string;
}

export class UpdateUserDto {
  @ApiProperty({ example: 'Tonia' })
  @IsString()
  @IsOptional()
  public readonly name: string;

  @ApiProperty({ example: '<EMAIL>' })
  @IsString()
  @IsOptional()
  public readonly phone: string;

  @ApiProperty({})
  @IsString()
  @IsOptional()
  public readonly email: string;

  @ApiProperty({})
  @IsOptional()
  @IsBoolean()
  public readonly auto_debit_wallets?: boolean;

  @ApiProperty({
    type: 'object',
    properties: {
      community_joined: {
        type: 'boolean',
      },
      pwa_added: {
        type: 'boolean',
      },
      onboarding_call_booked: {
        type: 'boolean',
      },
      has_followed_socials: {
        type: 'boolean',
      },
    },
  })
  @IsOptional()
  public readonly onboarding_steps: {
    community_joined: boolean;
    pwa_added: boolean;
    onboarding_call_booked: boolean;
    has_followed_socials: boolean;
  };
}

export class UpdateUserPasswordDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  public readonly current_password: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  public readonly new_password: string;
}

export class SendVerificationTokenUserDto {
  @ApiProperty()
  @IsOptional()
  email: true;

  @ApiProperty()
  @IsOptional()
  phone: true;
}

export class VerifyTokenUserDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  public readonly email: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  public readonly phone: string;
}

export class RequestPasswordResetUserDto extends PickType(VerifyTokenUserDto, ['email']) {}

export class ResetPasswordUserDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  token: string;
  x;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  password: string;
}

export class UpdateUserSubscriptionDto {
  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  status: boolean;

  @ApiProperty()
  @IsDateString()
  @IsOptional()
  next_payment_date: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  plan: string;
}

export class FilterUserDto {
  @ApiProperty({ required: false })
  search: string;
}

export class PushNotificationSubscriptionDto {
  @ApiProperty()
  @IsString()
  endpoint: string;

  @ApiProperty()
  @IsString()
  private_key: string;

  @ApiProperty()
  @IsString()
  public_key: string;

  @ApiProperty({ enum: COUNTRY_CODE })
  @IsOptional()
  @IsEnum(COUNTRY_CODE)
  country?: COUNTRY_CODE;
}

export class PushNotificationSubscriptionDtoWithCountry {
  @ApiProperty()
  @IsString()
  endpoint: string;

  @ApiProperty()
  @IsString()
  private_key: string;

  @ApiProperty()
  @IsString()
  public_key: string;

  @ApiProperty({ enum: COUNTRY_CODE })
  @IsNotEmpty()
  @IsEnum(COUNTRY_CODE)
  country: COUNTRY_CODE;
}

export class NotificationInfoDto {
  @ApiProperty({
    description: 'The title of the notification',
    example: 'Important Update',
  })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({
    description: 'The message content of the notification',
    example: 'We have an important update for you.',
  })
  @IsString()
  @IsNotEmpty()
  message: string;

  @ApiProperty({
    description: 'The path for the notification, used for navigation purposes',
    example: '/updates',
  })
  @IsString()
  @IsNotEmpty()
  path: string;

  @ApiProperty({
    description:
      'The user email to send the notification to. If provided, notification will be sent to this specific user.',
    example: '<EMAIL>',
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsEmail()
  user_email?: string;

  @ApiProperty({
    description:
      'Array of user emails to send the notification to. If provided, notification will be sent to these specific users.',
    example: ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
    required: false,
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @IsEmail({}, { each: true })
  emails?: string[];

  @ApiProperty({
    enum: COUNTRY_CODE,
    description:
      'The country code to send the notification to users in that country. If provided, notification will be sent to users in this country.',
    example: 'NG',
    required: false,
  })
  @IsOptional()
  @IsEnum(COUNTRY_CODE)
  country?: COUNTRY_CODE;

  @ApiProperty({
    enum: NOTIFICATION_TYPE,
    description: 'The type of notification',
    example: NOTIFICATION_TYPE.NEW_ORDER,
    required: false,
  })
  @IsOptional()
  @IsEnum(NOTIFICATION_TYPE)
  type?: NOTIFICATION_TYPE = NOTIFICATION_TYPE.NEW_ORDER;

  @ApiProperty({
    description: 'Additional data for the notification',
    example: { orderId: '12345' },
    required: false,
  })
  @IsOptional()
  @IsObject()
  data?: Record<string, any>;
}

export class ManuallyCreditWalletDto {
  @IsString()
  @IsNotEmpty()
  email: string;

  @IsNumber()
  @IsNotEmpty()
  amount: number;

  @IsString()
  @IsNotEmpty()
  otp: string;

  @IsString()
  @IsNotEmpty()
  narration: string;

  @IsString()
  @IsOptional()
  @IsEnum(COUNTRY_CODE)
  country?: COUNTRY_CODE;
}

export class ChangeCountryDto {
  @ApiProperty({
    description: 'The new country code to set for the user and all related data',
    enum: COUNTRY_CODE,
    example: 'NG',
  })
  @IsNotEmpty()
  @IsEnum(COUNTRY_CODE)
  country: COUNTRY_CODE;
}

export class AdminChangeCountryDto extends ChangeCountryDto {
  @ApiProperty({
    description: 'The email of the user to update',
    example: '<EMAIL>',
  })
  @IsString()
  @IsNotEmpty()
  @IsEmail()
  email: string;
}

export class InternalDashboardRoleDto {
  @ApiProperty({
    description: 'The internal dashboard role to assign to the user',
    enum: INTERNAL_ROLES,
    example: 'SUPPORT',
  })
  @IsNotEmpty()
  @IsEnum(INTERNAL_ROLES)
  role: string;
}

export class FirebaseSubscriptionDto {
  @ApiProperty({ description: 'FCM token' })
  @IsString()
  @IsNotEmpty()
  fcm_token: string;

  @ApiProperty({ enum: COUNTRY_CODE, default: COUNTRY_CODE.NG })
  @IsString()
  country: COUNTRY_CODE;
}
